shader_type canvas_item;

uniform float reflection_intensity : hint_range(0.0, 1.0) = 0.5;
uniform float reflection_speed : hint_range(0.0, 5.0) = 1.0;

void fragment() {
    vec4 texture_color = texture(TEXTURE, UV);
    
    // Créer un effet de reflet mobile
    float reflection = sin(UV.x * 10.0 + TIME * reflection_speed) * 0.5 + 0.5;
    reflection *= smoothstep(0.0, 0.3, UV.x) * smoothstep(1.0, 0.7, UV.x);
    reflection *= reflection_intensity;
    
    // Ajouter le reflet à la couleur de base
    COLOR = texture_color + vec4(reflection, reflection, reflection, 0.0);
}