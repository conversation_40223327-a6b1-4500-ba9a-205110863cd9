extends Node
class_name CardGameUI

const GameConfig = preload("res://Scripts/GameConfig.gd")

var card_hand: Control
var opponent_hand: Control
var hand_value_label: Label
var player1_indicator: Label
var player2_indicator: Label

func _ready() -> void:
	# Fix node paths
	card_hand = get_node("../CardHand")
	opponent_hand = get_node("../OpponentHand")
	hand_value_label = get_node("../HandValue")
	player1_indicator = get_node("../Player1Zone/Player1Label")
	player2_indicator = get_node("../Player2Zone/Player2Label")
	setup_ui()

func setup_ui() -> void:
	if not card_hand or not hand_value_label or not opponent_hand:
		push_error("Required UI nodes not found")
		return

	# Initialize user interface
	hand_value_label.text = "Select cards, then validate your hand"

	# Initialize player indicators
	if player1_indicator and player2_indicator:
		player1_indicator.text = "Player 1"
		player2_indicator.text = "Player 2 (AI)"

func update_ui(state: Dictionary) -> void:
	update_card_hand(state.player1_hand, state.selected_cards)
	update_opponent_hand(state.player2_hand)
	update_hand_value(state.selected_cards)
	update_player_indicators(get_parent().active_player)

func update_card_hand(hand: Array, selected_cards: Array) -> void:
	for child in card_hand.get_children():
		child.queue_free()

	for card in hand:
		var card_button := create_card_button(card)
		card_hand.add_child(card_button)
		# Apply effect immediately if card is selected
		if selected_cards.has(card.id):
			card_button.set_meta("selected", true)
			apply_selection_style(card_button)
		else:
			card_button.set_meta("selected", false)
			card_button.modulate = GameConfig.NORMAL_COLOR
			card_button.scale = Vector2.ONE

func create_card_button(card: Dictionary) -> TextureButton:
	var button := TextureButton.new()
	button.custom_minimum_size = Vector2(GameConfig.CARD_WIDTH, GameConfig.CARD_HEIGHT)

	# Chargement de la texture en fonction de la couleur de la carte
	var texture_path = "res://Assets/cards/Top-Down/Cards/"
	match card.suit.to_lower():
		"hearts":
			texture_path += "Hearts.png"
		"diamonds":
			texture_path += "Diamonds.png"
		"clubs":
			texture_path += "Clubs.png"
		"spades":
			texture_path += "Spades.png"

	var spritesheet = load(texture_path)
	var atlas = AtlasTexture.new()
	atlas.atlas = spritesheet

	# Calcul de la position de la carte dans le spritesheet
	var rank = card.value - 1  # -1 car les rangs commencent à 1
	var x = (rank % 5) * (86 + 2)  # 5 cartes par ligne, 86px de large + 2px d'espacement
	var y = int(rank / 5) * (122 + 2) # 122px de haut + 2px d'espacement

	atlas.region = Rect2(x, y, 86, 122)
	button.texture_normal = atlas
	button.set_meta("card_id", card.id)

	# Ajouter l'indicateur de modification si la carte a été modifiée
	if card.has("modified") and card.modified:
		add_modification_indicator(button)

	# Connecter le clic gauche pour sélectionner la carte
	button.pressed.connect(_on_card_pressed.bind(card.id))

	# Connecter le clic droit pour désélectionner toutes les cartes
	button.mouse_entered.connect(_on_card_mouse_entered.bind(card.id))
	button.mouse_exited.connect(_on_card_mouse_exited.bind(card.id))

	# Activer la détection du clic droit
	button.mouse_default_cursor_shape = Control.CURSOR_POINTING_HAND
	button.set_default_cursor_shape(Control.CURSOR_POINTING_HAND)

	# Connecter le signal pour le clic droit
	button.gui_input.connect(_on_card_gui_input)

	return button

# Ajoute un indicateur visuel sur une carte modifiée
func add_modification_indicator(button: TextureButton) -> void:
	# Charger la texture de l'indicateur de modification
	var scribble_texture = load("res://Assets/Scribble.png")
	if not scribble_texture:
		push_error("Impossible de charger la texture Scribble.png")
		return

	# Créer un sprite pour l'indicateur
	var scribble = TextureRect.new()
	scribble.texture = scribble_texture
	scribble.expand = true
	scribble.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT
	scribble.size = button.custom_minimum_size
	scribble.mouse_filter = Control.MOUSE_FILTER_IGNORE  # Pour que les clics passent à travers

	# Ajouter l'indicateur à la carte
	button.add_child(scribble)
	button.set_meta("modified", true)

func _on_card_pressed(card_id: int) -> void:
	get_parent().get_node("HandManager").select_card(card_id)

# Handle GUI inputs to detect right click
func _on_card_gui_input(event: InputEvent) -> void:
	if event is InputEventMouseButton and event.button_index == 2 and event.pressed: # 2 = right mouse button
		# Deselect all cards with right click
		get_parent().get_node("HandManager").clear_selection()

func _on_card_mouse_entered(card_id: int) -> void:
	get_parent().get_node("EffectManager").play_hover_effect(card_id)

func _on_card_mouse_exited(card_id: int) -> void:
	get_parent().get_node("EffectManager").stop_hover_effect(card_id)

# Affiche les cartes de l'adversaire (faces cachées)
func update_opponent_hand(hand: Array) -> void:
	# Nettoyer les cartes existantes
	for child in opponent_hand.get_children():
		child.queue_free()

	# Créer les cartes de l'adversaire (faces cachées)
	for i in range(hand.size()):
		var card_back = create_card_back(hand[i])
		opponent_hand.add_child(card_back)

# Crée une carte face cachée pour l'adversaire
func create_card_back(card: Dictionary) -> TextureRect:
	var card_back = TextureRect.new()
	card_back.texture = load("res://Assets/cards/Top-Down/Cards/blueCardBack.png")
	card_back.expand = true
	card_back.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT
	card_back.custom_minimum_size = Vector2(GameConfig.CARD_WIDTH * 0.8, GameConfig.CARD_HEIGHT * 0.8)
	card_back.set_meta("card_id", card.id)

	# Si la carte a été modifiée, ajouter l'indicateur
	if card.has("modified") and card.modified:
		add_modification_indicator_to_back(card_back)

	return card_back

# Ajoute un indicateur de modification à une carte face cachée
func add_modification_indicator_to_back(card_back: TextureRect) -> void:
	# Charger la texture de l'indicateur de modification
	var scribble_texture = load("res://Assets/Scribble.png")
	if not scribble_texture:
		push_error("Impossible de charger la texture Scribble.png")
		return

	# Créer un sprite pour l'indicateur
	var scribble = TextureRect.new()
	scribble.texture = scribble_texture
	scribble.expand = true
	scribble.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_COVERED
	scribble.size = card_back.custom_minimum_size
	scribble.mouse_filter = Control.MOUSE_FILTER_IGNORE

	# Ajouter l'indicateur à la carte
	card_back.add_child(scribble)
	card_back.set_meta("modified", true)

# Met à jour les indicateurs de joueur actif
func update_player_indicators(active_player: int) -> void:
	if player1_indicator and player2_indicator:
		if active_player == 1:
			player1_indicator.text = "Joueur 1 (ACTIF)"
			player1_indicator.add_theme_color_override("font_color", Color(1, 0.8, 0, 1))
			player2_indicator.text = "Joueur 2 (IA)"
			player2_indicator.add_theme_color_override("font_color", Color(1, 1, 1, 1))
		else:
			player1_indicator.text = "Joueur 1"
			player1_indicator.add_theme_color_override("font_color", Color(1, 1, 1, 1))
			player2_indicator.text = "Joueur 2 (IA) (ACTIF)"
			player2_indicator.add_theme_color_override("font_color", Color(1, 0.8, 0, 1))

func apply_selection_style(button: TextureButton) -> void:
	# Appliquer directement les effets visuels
	button.modulate = GameConfig.SELECTED_COLOR
	button.scale = GameConfig.SELECTION_SCALE

	# Position initiale + décalage vers le haut
	var current_pos = button.position
	button.position.y = current_pos.y - GameConfig.SELECTION_LIFT

	# Appeler l'effet de sélection
	get_parent().get_node("EffectManager").play_card_selection_effect(button.get_meta("card_id"))

func update_hand_value(selected_cards: Array) -> void:
	var poker_hand = get_parent().get_node("HandManager").poker_hand_evaluator
	var selected_card_objects = []

	# Convertir les IDs de cartes sélectionnées en objets de carte
	var player_hand = get_parent().get_node("HandManager").player1_hand
	for card in player_hand:
		if selected_cards.has(card.id):
			selected_card_objects.append({
				"rank": card.value,
				"suit": card.suit
			})

	var hand_value = poker_hand.evaluate_hand(selected_card_objects)
	hand_value_label.text = hand_value
