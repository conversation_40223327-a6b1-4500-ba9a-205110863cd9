extends Control
class_name SimpleGameController

# Managers
var game_manager: SimpleBestOfThree
var hand_manager: HandManager
var ai_player: SimpleAI
var ui_manager: SimpleGameUI

# UI Layout
@onready var main_container: VBoxContainer = $MainContainer

func _ready():
	setup_managers()
	setup_ui()
	connect_signals()
	start_game()

func setup_managers():
	# Create managers
	game_manager = SimpleBestOfThree.new()
	add_child(game_manager)
	
	hand_manager = HandManager.new()
	add_child(hand_manager)
	
	ai_player = SimpleAI.new()
	add_child(ai_player)
	
	# Set references
	game_manager.set_hand_manager(hand_manager)
	ai_player.set_managers(game_manager, hand_manager)

func setup_ui():
	# Create main container if it doesn't exist
	if not main_container:
		main_container = VBoxContainer.new()
		add_child(main_container)
	
	# Create UI manager
	ui_manager = SimpleGameUI.new()
	ui_manager.set_managers(game_manager, hand_manager)
	main_container.add_child(ui_manager)

func connect_signals():
	# Game signals
	game_manager.round_ended.connect(_on_round_ended)
	game_manager.game_ended.connect(_on_game_ended)

func start_game():
	print("=== STARTING SIMPLE BEST OF THREE ===")
	game_manager.start_new_game()
	
	# Refresh UI
	if ui_manager:
		ui_manager.refresh_display()

func _on_round_ended(winner: int, results: Dictionary):
	print("Round ended - Winner: ", winner)
	print("Reason: ", results.reason)
	
	# Show continue button after a delay
	await get_tree().create_timer(3.0).timeout
	show_continue_button()

func show_continue_button():
	# Check if game is over
	var scores = game_manager.get_scores()
	if scores.player1 >= 2 or scores.player2 >= 2 or game_manager.get_current_round() >= 3:
		return  # Game ended, no continue button
	
	# Create continue button
	var continue_button = Button.new()
	continue_button.text = "Next Round"
	continue_button.custom_minimum_size = Vector2(150, 40)
	
	# Add to UI
	main_container.add_child(continue_button)
	
	# Connect signal
	continue_button.pressed.connect(_on_continue_pressed)

func _on_continue_pressed():
	# Remove continue button
	for child in main_container.get_children():
		if child is Button and child.text == "Next Round":
			child.queue_free()
	
	# Start next round
	game_manager.next_round()
	
	# Refresh UI
	if ui_manager:
		ui_manager.refresh_display()

func _on_game_ended(final_winner: int, scores: Dictionary):
	print("Game ended - Final winner: ", final_winner)
	print("Final scores: ", scores)
	
	# Show restart button
	show_restart_button()

func show_restart_button():
	# Create restart button
	var restart_button = Button.new()
	restart_button.text = "Play Again"
	restart_button.custom_minimum_size = Vector2(150, 40)
	
	# Add to UI
	main_container.add_child(restart_button)
	
	# Connect signal
	restart_button.pressed.connect(_on_restart_pressed)

func _on_restart_pressed():
	# Reload scene
	get_tree().reload_current_scene()

# Debug function
func _input(event):
	if event.is_action_pressed("ui_accept"):  # Enter key
		print_debug_info()

func print_debug_info():
	print("=== DEBUG INFO ===")
	if game_manager:
		print("Phase: ", game_manager.get_current_phase())
		print("Round: ", game_manager.get_current_round())
		print("Scores: ", game_manager.get_scores())
		print("Player 1 selected: ", game_manager.get_selected_cards(1))
		print("Player 2 selected: ", game_manager.get_selected_cards(2))
		print("Player 1 proceeded: ", game_manager.has_player_proceeded(1))
		print("Player 2 proceeded: ", game_manager.has_player_proceeded(2))
		print("Player 1 modified: ", game_manager.has_player_modified(1))
		print("Player 2 modified: ", game_manager.has_player_modified(2))
		print("Player 1 validated: ", game_manager.has_player_validated(1))
		print("Player 2 validated: ", game_manager.has_player_validated(2))
	
	if hand_manager:
		print("Player 1 hand size: ", hand_manager.player1_hand.size())
		print("Player 2 hand size: ", hand_manager.player2_hand.size())
	
	print("===================")
