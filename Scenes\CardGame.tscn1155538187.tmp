[gd_scene load_steps=3 format=3 uid="uid://cil6aa3jpw4oq"]

[ext_resource type="Script" uid="uid://booced0hj67a6" path="res://Scripts/CardGame.gd" id="1_game"]
[ext_resource type="Texture2D" uid="uid://k47sxer7yqgy" path="res://Assets/cards/Top-Down/Cards/blueCardBack.png" id="3_c1akp"]

[node name="Root" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_game")

[node name="MainLayout" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="OpponentHand" type="HBoxContainer" parent="MainLayout"]
layout_mode = 2
alignment = 1

[node name="Card1" type="TextureButton" parent="MainLayout/OpponentHand"]
custom_minimum_size = Vector2(100, 150)
layout_mode = 2

[node name="Card2" type="TextureButton" parent="MainLayout/OpponentHand"]
custom_minimum_size = Vector2(100, 150)
layout_mode = 2

[node name="Card3" type="TextureButton" parent="MainLayout/OpponentHand"]
custom_minimum_size = Vector2(100, 150)
layout_mode = 2

[node name="Card4" type="TextureButton" parent="MainLayout/OpponentHand"]
custom_minimum_size = Vector2(100, 150)
layout_mode = 2

[node name="GameArea" type="MarginContainer" parent="MainLayout"]
layout_mode = 2
size_flags_vertical = 3

[node name="CardHand" type="HBoxContainer" parent="MainLayout"]
layout_mode = 2
theme_override_constants/separation = 10
alignment = 1

[node name="Card1" type="TextureButton" parent="MainLayout/CardHand"]
custom_minimum_size = Vector2(100, 150)
layout_mode = 2

[node name="Card2" type="TextureButton" parent="MainLayout/CardHand"]
custom_minimum_size = Vector2(100, 150)
layout_mode = 2

[node name="Card3" type="TextureButton" parent="MainLayout/CardHand"]
custom_minimum_size = Vector2(100, 150)
layout_mode = 2

[node name="Card4" type="TextureButton" parent="MainLayout/CardHand"]
custom_minimum_size = Vector2(100, 150)
layout_mode = 2

[node name="Card5" type="TextureButton" parent="MainLayout/CardHand"]
custom_minimum_size = Vector2(100, 150)
layout_mode = 2

[node name="Card6" type="TextureButton" parent="MainLayout/CardHand"]
custom_minimum_size = Vector2(100, 150)
layout_mode = 2

[node name="Card7" type="TextureButton" parent="MainLayout/CardHand"]
custom_minimum_size = Vector2(100, 150)
layout_mode = 2

[node name="Card8" type="TextureButton" parent="MainLayout/CardHand"]
custom_minimum_size = Vector2(100, 150)
layout_mode = 2

[node name="HandValue" type="Label" parent="MainLayout"]
layout_mode = 2
text = "Sélectionnez des cartes pour former une main"
horizontal_alignment = 1

[node name="ConfirmButton" type="Button" parent="MainLayout"]
layout_mode = 2
text = "Confirmer"

[node name="PlayHandButton" type="Button" parent="MainLayout"]
layout_mode = 2
text = "Jouer la main"

[node name="MenuButton" type="Button" parent="."]
layout_mode = 0
offset_left = 1098.0
offset_top = 16.0
offset_right = 1273.0
offset_bottom = 96.0
text = "Retour au menu"

[node name="BestHandButton" type="Button" parent="."]
layout_mode = 0
offset_left = 30.0
offset_top = 270.0
offset_right = 170.0
offset_bottom = 330.0
text = "Meilleur Main"

[node name="CheatRankButton" type="Button" parent="."]
layout_mode = 0
offset_left = 30.0
offset_top = 350.0
offset_right = 170.0
offset_bottom = 410.0
text = "Changer rang"

[node name="CheatColorButton" type="Button" parent="."]
layout_mode = 0
offset_left = 30.0
offset_top = 430.0
offset_right = 170.0
offset_bottom = 490.0
text = "Changer couleur"

[node name="Player1Zone" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.327
anchor_top = 0.319
anchor_right = 0.62
anchor_bottom = 0.658
offset_left = 0.151978
offset_top = 0.00799561
offset_right = 0.119995
offset_bottom = -0.344055
grow_horizontal = 2
grow_vertical = 2

[node name="Player1Label" type="Label" parent="Player1Zone"]
layout_mode = 2
text = "Joueur 1"
horizontal_alignment = 2

[node name="Player1Hand" type="GridContainer" parent="Player1Zone"]
layout_mode = 2
size_flags_vertical = 3
columns = 3

[node name="Player2Zone" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.625
anchor_top = 0.319
anchor_right = 0.928
anchor_bottom = 0.658
offset_top = 0.00799561
offset_right = -0.272034
offset_bottom = -0.344055
grow_horizontal = 2
grow_vertical = 2

[node name="Player2Label" type="Label" parent="Player2Zone"]
layout_mode = 2
text = "Joueur 2"
horizontal_alignment = 2

[node name="Player2Hand" type="GridContainer" parent="Player2Zone"]
layout_mode = 2
size_flags_vertical = 3
columns = 3

[node name="WinnerScreen" type="Panel" parent="."]
visible = false
layout_mode = 0
offset_left = 156.0
offset_top = 62.0
offset_right = 1119.0
offset_bottom = 642.0

[node name="Label" type="Label" parent="WinnerScreen"]
layout_mode = 0
offset_left = 69.0
offset_top = 48.0
offset_right = 877.0
offset_bottom = 524.0
text = "GAGNANT : "
horizontal_alignment = 1
vertical_alignment = 1

[node name="ChangeRankPanel" type="Panel" parent="."]
visible = false
layout_mode = 0
offset_left = 190.0
offset_top = 430.0
offset_right = 490.0
offset_bottom = 610.0

[node name="Label" type="Label" parent="ChangeRankPanel"]
layout_mode = 0
offset_left = 10.0
offset_right = 280.0
offset_bottom = 50.0
text = "Modifiez le rang de votre carte"

[node name="AddOneButton" type="Button" parent="ChangeRankPanel"]
layout_mode = 0
offset_left = 42.0
offset_top = 77.0
offset_right = 118.0
offset_bottom = 139.0
text = "+1"

[node name="SubtractOneButton" type="Button" parent="ChangeRankPanel"]
layout_mode = 0
offset_left = 171.0
offset_top = 79.0
offset_right = 241.0
offset_bottom = 138.0
text = "-1"

[node name="ChangeColorPanel" type="Panel" parent="."]
visible = false
layout_mode = 0
offset_left = 190.0
offset_top = 430.0
offset_right = 490.0
offset_bottom = 610.0

[node name="Label" type="Label" parent="ChangeColorPanel"]
layout_mode = 0
offset_left = 10.0
offset_right = 280.0
offset_bottom = 50.0
text = "Modifiez la couleur de votre carte"

[node name="ItemList" type="ItemList" parent="ChangeColorPanel"]
layout_mode = 0
offset_left = 10.0
offset_top = 60.0
offset_right = 290.0
offset_bottom = 160.0
item_count = 4
item_0/text = "Coeurs"
item_1/text = "Carreaux"
item_2/text = "Trèfles"
item_3/text = "Piques"

[node name="OpponentCardBack" type="Node2D" parent="."]

[node name="BlueCardBack1" type="Sprite2D" parent="OpponentCardBack"]
position = Vector2(401, 53)
rotation = 0.801768
texture = ExtResource("3_c1akp")

[node name="BlueCardBack2" type="Sprite2D" parent="OpponentCardBack"]
position = Vector2(436, 81)
rotation = 0.487617
texture = ExtResource("3_c1akp")

[node name="BlueCardBack3" type="Sprite2D" parent="OpponentCardBack"]
position = Vector2(503, 106)
rotation = 0.260195
texture = ExtResource("3_c1akp")

[node name="BlueCardBack4" type="Sprite2D" parent="OpponentCardBack"]
position = Vector2(577, 121)
rotation = 0.0857317
texture = ExtResource("3_c1akp")

[node name="BlueCardBack5" type="Sprite2D" parent="OpponentCardBack"]
position = Vector2(653, 119)
rotation = -0.147502
texture = ExtResource("3_c1akp")

[node name="BlueCardBack6" type="Sprite2D" parent="OpponentCardBack"]
position = Vector2(728, 112)
rotation = -0.250675
texture = ExtResource("3_c1akp")

[node name="BlueCardBack7" type="Sprite2D" parent="OpponentCardBack"]
position = Vector2(792, 90)
rotation = -0.507645
texture = ExtResource("3_c1akp")

[node name="BlueCardBack8" type="Sprite2D" parent="OpponentCardBack"]
position = Vector2(847, 59)
rotation = -0.644539
texture = ExtResource("3_c1akp")

[node name="Background" type="ColorRect" parent="."]
z_index = -1
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0.0784314, 0.380392, 0.141176, 1)

[node name="TableDecoration" type="Node2D" parent="."]
