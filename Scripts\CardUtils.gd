extends Node
class_name CardUtils

# Card constants
const SUITS = ["hearts", "diamonds", "clubs", "spades"]
const RANKS = ["A", "2", "3", "4", "5", "6", "7", "8", "9", "10", "J", "Q", "K"]
const MIN_RANK = 1
const MAX_RANK = 13

# Generate a random hand of cards
static func generate_random_hand(size: int) -> Array:
	var hand := []
	for i in range(size):
		hand.append(create_random_card())
	return hand

# Create a single random card
static func create_random_card() -> Dictionary:
	return {
		"id": randi(),
		"value": randi() % MAX_RANK + MIN_RANK,
		"suit": SUITS[randi() % SUITS.size()],
		"modified": false
	}

# Convert card information to evaluator format
static func convert_cards_for_evaluation(cards: Array) -> Array:
	var eval_cards = []
	for card in cards:
		eval_cards.append({
			"rank": card.get("value", card.get("rank", 1)),
			"suit": card.get("suit", "hearts")
		})
	return eval_cards

# Find a card by ID in a hand
static func find_card_by_id(hand: Array, card_id: int) -> Dictionary:
	for card in hand:
		if card.get("id") == card_id:
			return card
	return {}

# Get multiple cards by their IDs
static func get_cards_by_ids(hand: Array, card_ids: Array) -> Array:
	var cards = []
	for card_id in card_ids:
		var card = find_card_by_id(hand, card_id)
		if not card.is_empty():
			cards.append(card)
	return cards

# Validate card data structure
static func is_valid_card(card: Dictionary) -> bool:
	return card.has("id") and card.has("value") and card.has("suit")

# Get card display name
static func get_card_display_name(card: Dictionary) -> String:
	if not is_valid_card(card):
		return "Invalid Card"
	
	var rank_name = RANKS[card.value - 1] if card.value <= RANKS.size() else str(card.value)
	var suit_name = card.suit.capitalize()
	return rank_name + " of " + suit_name
