extends Node
class_name CardUtils

# Card constants
const SUITS = ["hearts", "diamonds", "clubs", "spades"]
const RANKS = ["A", "2", "3", "4", "5", "6", "7", "8", "9", "10", "J", "Q", "K"]
const MIN_RANK = 1
const MAX_RANK = 13

# Card types
enum CardType {
	NORMAL,    # Regular playing card
	SPECIAL    # Special power card
}

# Special card powers
enum SpecialPower {
	NONE,
	REMOVE_OPPONENT_CARD,     # "Remove one card from opponent's hand"
	PEEK_OPPONENT_HAND,       # "Look at opponent's hand"
	FORCE_DISCARD,            # "Force opponent to discard a card"
	SWAP_CARDS,               # "Swap one of your cards with opponent's"
	BLOCK_MODIFICATION,       # "Opponent cannot modify cards this round"
	DOUBLE_MODIFICATION       # "You can modify 2 cards this round"
}

# Generate a random hand of cards with better distribution
static func generate_random_hand(size: int) -> Array:
	var hand := []
	var used_ids := {}

	for i in range(size):
		var card = create_random_card()

		# Ensure unique IDs
		while used_ids.has(card.id):
			card.id = randi()
		used_ids[card.id] = true

		hand.append(card)
	return hand

# Create a single random card with better randomization
static func create_random_card() -> Dictionary:
	# Use time-based seed for better randomization
	var time_seed = Time.get_unix_time_from_system()
	var random_seed = int(time_seed * 1000) + randi()

	return {
		"id": random_seed + randi(),
		"value": randi() % MAX_RANK + MIN_RANK,
		"suit": SUITS[randi() % SUITS.size()],
		"modified": false,
		"type": CardType.NORMAL,
		"special_power": SpecialPower.NONE
	}

# Create a special power card
static func create_special_card(power: SpecialPower) -> Dictionary:
	return {
		"id": randi() + Time.get_unix_time_from_system(),
		"value": 0,  # Special cards have no poker value
		"suit": "special",
		"modified": false,
		"type": CardType.SPECIAL,
		"special_power": power,
		"name": get_special_card_name(power),
		"description": get_special_card_description(power)
	}

# Get special card name
static func get_special_card_name(power: SpecialPower) -> String:
	match power:
		SpecialPower.REMOVE_OPPONENT_CARD:
			return "Card Removal"
		SpecialPower.PEEK_OPPONENT_HAND:
			return "Peek"
		SpecialPower.FORCE_DISCARD:
			return "Force Discard"
		SpecialPower.SWAP_CARDS:
			return "Card Swap"
		SpecialPower.BLOCK_MODIFICATION:
			return "Block"
		SpecialPower.DOUBLE_MODIFICATION:
			return "Double Modify"
		_:
			return "Unknown"

# Get special card description
static func get_special_card_description(power: SpecialPower) -> String:
	match power:
		SpecialPower.REMOVE_OPPONENT_CARD:
			return "Remove one card from opponent's hand"
		SpecialPower.PEEK_OPPONENT_HAND:
			return "Look at opponent's hand"
		SpecialPower.FORCE_DISCARD:
			return "Force opponent to discard a card"
		SpecialPower.SWAP_CARDS:
			return "Swap one of your cards with opponent's"
		SpecialPower.BLOCK_MODIFICATION:
			return "Opponent cannot modify cards this round"
		SpecialPower.DOUBLE_MODIFICATION:
			return "You can modify 2 cards this round"
		_:
			return "Unknown power"

# Convert card information to evaluator format
static func convert_cards_for_evaluation(cards: Array) -> Array:
	var eval_cards = []
	for card in cards:
		eval_cards.append({
			"rank": card.get("value", card.get("rank", 1)),
			"suit": card.get("suit", "hearts")
		})
	return eval_cards

# Find a card by ID in a hand
static func find_card_by_id(hand: Array, card_id: int) -> Dictionary:
	for card in hand:
		if card.get("id") == card_id:
			return card
	return {}

# Get multiple cards by their IDs
static func get_cards_by_ids(hand: Array, card_ids: Array) -> Array:
	var cards = []
	for card_id in card_ids:
		var card = find_card_by_id(hand, card_id)
		if not card.is_empty():
			cards.append(card)
	return cards

# Validate card data structure
static func is_valid_card(card: Dictionary) -> bool:
	return card.has("id") and card.has("value") and card.has("suit")

# Get card display name
static func get_card_display_name(card: Dictionary) -> String:
	if not is_valid_card(card):
		return "Invalid Card"

	var rank_name = RANKS[card.value - 1] if card.value <= RANKS.size() else str(card.value)
	var suit_name = card.suit.capitalize()
	return rank_name + " of " + suit_name

# Create a deep copy of card data
static func copy_card(card: Dictionary) -> Dictionary:
	return {
		"id": card.get("id", randi()),
		"value": card.get("value", 1),
		"suit": card.get("suit", "hearts"),
		"modified": card.get("modified", false)
	}

# Create a deep copy of a hand
static func copy_hand(hand: Array) -> Array:
	var copied_hand = []
	for card in hand:
		copied_hand.append(copy_card(card))
	return copied_hand

# Generate a more realistic hand from a deck (no duplicates)
static func generate_realistic_hand(size: int) -> Array:
	var deck = create_full_deck()
	var hand = []

	# Shuffle deck
	for i in range(deck.size()):
		var j = randi() % deck.size()
		var temp = deck[i]
		deck[i] = deck[j]
		deck[j] = temp

	# Take first 'size' cards
	for i in range(min(size, deck.size())):
		hand.append(deck[i])

	return hand

# Create a full 52-card deck
static func create_full_deck() -> Array:
	var deck = []
	var card_id = 1

	for suit in SUITS:
		for rank in range(MIN_RANK, MAX_RANK + 1):
			deck.append({
				"id": card_id,
				"value": rank,
				"suit": suit,
				"modified": false,
				"type": CardType.NORMAL,
				"special_power": SpecialPower.NONE
			})
			card_id += 1

	return deck

# Check if a card is a special card
static func is_special_card(card: Dictionary) -> bool:
	return card.get("type", CardType.NORMAL) == CardType.SPECIAL

# Filter out special cards from hand for poker evaluation
static func filter_normal_cards(cards: Array) -> Array:
	var normal_cards = []
	for card in cards:
		if not is_special_card(card):
			normal_cards.append(card)
	return normal_cards
