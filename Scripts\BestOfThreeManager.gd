extends Node
class_name BestOfThreeManager

signal round_started(round_number: int)
signal phase_changed(new_phase: String)
signal round_ended(winner: int, results: Dictionary)
signal game_ended(final_winner: int, scores: Dictionary)

# Game phases
enum GamePhase {
	CARD_SELECTION,     # Players select cards from their hand
	MODIFICATION,       # Players can exchange (legal) or modify (illegal) cards
	VALIDATION,         # Players validate their final hand
	REVEAL,             # Hands are revealed and compared
	ROUND_END          # Show round results
}

# Game state
var current_round: int = 1
var current_phase: GamePhase = GamePhase.CARD_SELECTION
var player1_score: int = 0
var player2_score: int = 0

# Round state
var player1_selected_cards: Array = []
var player2_selected_cards: Array = []
var player1_validated: bool = false
var player2_validated: bool = false
var player1_modified: bool = false
var player2_modified: bool = false
var player1_modification_type: String = ""  # "exchange", "illegal", or ""
var player2_modification_type: String = ""

# Special cards state
var player1_special_cards: Array = []
var player2_special_cards: Array = []
var special_effects_active: Dictionary = {}

# Managers
var hand_manager: HandManager

func _ready():
	pass

func set_hand_manager(manager: HandManager):
	hand_manager = manager

# Start a new best-of-three game
func start_new_game():
	current_round = 1
	player1_score = 0
	player2_score = 0
	reset_round_state()
	start_round()

# Start a new round
func start_round():
	reset_round_state()
	current_phase = GamePhase.CARD_SELECTION
	
	# Generate new hands for both players
	if hand_manager:
		hand_manager.initialize_solo_game()
		
		# Add special cards (future feature - currently disabled)
		# add_special_cards_to_hands()
	
	emit_signal("round_started", current_round)
	emit_signal("phase_changed", "card_selection")
	print("Round ", current_round, " started - Card Selection Phase")

# Player selects cards from their hand
func select_cards(player_id: int, card_ids: Array) -> bool:
	if current_phase != GamePhase.CARD_SELECTION:
		print("Cannot select cards in current phase")
		return false
	
	if card_ids.size() < 1 or card_ids.size() > 5:
		print("Must select 1-5 cards")
		return false
	
	if player_id == 1:
		player1_selected_cards = card_ids.duplicate()
		print("Player 1 selected ", card_ids.size(), " cards")
	elif player_id == 2:
		player2_selected_cards = card_ids.duplicate()
		print("Player 2 selected ", card_ids.size(), " cards")
	else:
		return false
	
	return true

# Move to modification phase
func proceed_to_modification():
	if current_phase != GamePhase.CARD_SELECTION:
		return false
	
	if player1_selected_cards.is_empty() or player2_selected_cards.is_empty():
		print("Both players must select cards first")
		return false
	
	current_phase = GamePhase.MODIFICATION
	emit_signal("phase_changed", "modification")
	print("Modification Phase - Players can exchange or modify cards")
	return true

# Player exchanges a card (legal action)
func exchange_card(player_id: int, card_to_remove_id: int, card_to_add_id: int) -> bool:
	if current_phase != GamePhase.MODIFICATION:
		print("Cannot exchange cards in current phase")
		return false
	
	var selected_cards = player1_selected_cards if player_id == 1 else player2_selected_cards
	var player_hand = hand_manager.player1_hand if player_id == 1 else hand_manager.player2_hand
	
	# Check if card to remove is in selected cards
	if not selected_cards.has(card_to_remove_id):
		print("Card to remove is not in selected cards")
		return false
	
	# Check if card to add is in player's hand but not selected
	var card_to_add_found = false
	for card in player_hand:
		if card.id == card_to_add_id and not selected_cards.has(card_to_add_id):
			card_to_add_found = true
			break
	
	if not card_to_add_found:
		print("Card to add is not available")
		return false
	
	# Perform the exchange
	var remove_index = selected_cards.find(card_to_remove_id)
	selected_cards[remove_index] = card_to_add_id
	
	# Update state
	if player_id == 1:
		player1_selected_cards = selected_cards
		player1_modified = true
		player1_modification_type = "exchange"
		print("Player 1 exchanged a card (legal)")
	else:
		player2_selected_cards = selected_cards
		player2_modified = true
		player2_modification_type = "exchange"
		print("Player 2 exchanged a card (legal)")
	
	return true

# Player modifies a card (illegal action)
func modify_card(player_id: int, card_id: int, modification_type: String, new_value) -> bool:
	if current_phase != GamePhase.MODIFICATION:
		print("Cannot modify cards in current phase")
		return false
	
	var selected_cards = player1_selected_cards if player_id == 1 else player2_selected_cards
	
	# Check if card is in selected cards
	if not selected_cards.has(card_id):
		print("Card to modify is not in selected cards")
		return false
	
	# Apply modification through hand manager
	var success = false
	if modification_type == "rank":
		success = hand_manager.modify_card_rank(card_id, new_value)
	elif modification_type == "suit":
		success = hand_manager.modify_card_suit(card_id, new_value)
	
	if success:
		# Update state
		if player_id == 1:
			player1_modified = true
			player1_modification_type = "illegal"
			print("Player 1 modified a card (illegal)")
		else:
			player2_modified = true
			player2_modification_type = "illegal"
			print("Player 2 modified a card (illegal)")
	
	return success

# Player validates their hand (ends their turn)
func validate_hand(player_id: int) -> bool:
	if current_phase != GamePhase.MODIFICATION and current_phase != GamePhase.VALIDATION:
		print("Cannot validate hand in current phase")
		return false
	
	if player_id == 1:
		if player1_selected_cards.is_empty():
			print("Player 1 must select cards first")
			return false
		player1_validated = true
		print("Player 1 validated their hand")
	elif player_id == 2:
		if player2_selected_cards.is_empty():
			print("Player 2 must select cards first")
			return false
		player2_validated = true
		print("Player 2 validated their hand")
	else:
		return false
	
	# Check if both players have validated
	if player1_validated and player2_validated:
		proceed_to_reveal()
	elif current_phase == GamePhase.MODIFICATION:
		current_phase = GamePhase.VALIDATION
		emit_signal("phase_changed", "validation")
		print("Validation Phase - Waiting for other player")
	
	return true

# Proceed to reveal phase
func proceed_to_reveal():
	current_phase = GamePhase.REVEAL
	emit_signal("phase_changed", "reveal")
	
	# Determine round winner
	var results = determine_round_winner()
	
	# Update scores
	if results.winner == 1:
		player1_score += 1
	elif results.winner == 2:
		player2_score += 1
	
	emit_signal("round_ended", results.winner, results)
	
	# Check if game is over
	if player1_score >= 2 or player2_score >= 2 or current_round >= 3:
		end_game()
	else:
		current_phase = GamePhase.ROUND_END
		emit_signal("phase_changed", "round_end")

# Determine the winner of the current round
func determine_round_winner() -> Dictionary:
	var poker_evaluator = load("res://Scripts/PokerHand.gd").new()
	
	# Get card information
	var p1_cards_info = hand_manager.get_cards_info(player1_selected_cards)
	var p2_cards_info = hand_manager.get_cards_info(player2_selected_cards)
	
	# Evaluate hands
	var p1_hand_value = hand_manager.evaluate_hand(p1_cards_info)
	var p2_hand_value = hand_manager.evaluate_hand(p2_cards_info)
	
	# Get hand scores
	var p1_score = hand_manager.get_hand_score(p1_hand_value)
	var p2_score = hand_manager.get_hand_score(p2_hand_value)
	
	var winner = 0
	var reason = ""
	
	# Apply legality rules
	var p1_cheated = player1_modification_type == "illegal"
	var p2_cheated = player2_modification_type == "illegal"
	
	if p1_cheated and not p2_cheated:
		winner = 2
		reason = "Player 1 cheated, Player 2 wins!"
	elif p2_cheated and not p1_cheated:
		winner = 1
		reason = "Player 2 cheated, Player 1 wins!"
	elif p1_cheated and p2_cheated:
		# Both cheated, compare hands normally
		if p1_score > p2_score:
			winner = 1
			reason = "Both cheated - Player 1 wins with " + p1_hand_value
		elif p2_score > p1_score:
			winner = 2
			reason = "Both cheated - Player 2 wins with " + p2_hand_value
		else:
			winner = 0
			reason = "Both cheated - Tie with " + p1_hand_value
	else:
		# No cheating, compare hands normally
		if p1_score > p2_score:
			winner = 1
			reason = "Player 1 wins with " + p1_hand_value + " vs " + p2_hand_value
		elif p2_score > p1_score:
			winner = 2
			reason = "Player 2 wins with " + p2_hand_value + " vs " + p1_hand_value
		else:
			winner = 0
			reason = "Tie with " + p1_hand_value
	
	return {
		"winner": winner,
		"reason": reason,
		"player1_hand": p1_hand_value,
		"player2_hand": p2_hand_value,
		"player1_cards": p1_cards_info,
		"player2_cards": p2_cards_info,
		"player1_cheated": p1_cheated,
		"player2_cheated": p2_cheated,
		"player1_modification": player1_modification_type,
		"player2_modification": player2_modification_type
	}

# Start next round
func next_round():
	if current_phase != GamePhase.ROUND_END:
		return false
	
	current_round += 1
	start_round()
	return true

# End the game
func end_game():
	var final_winner = 1 if player1_score > player2_score else (2 if player2_score > player1_score else 0)
	var final_scores = {"player1": player1_score, "player2": player2_score}
	emit_signal("game_ended", final_winner, final_scores)
	print("Game ended - Final winner: ", final_winner, " Scores: ", final_scores)

# Reset round-specific state
func reset_round_state():
	player1_selected_cards.clear()
	player2_selected_cards.clear()
	player1_validated = false
	player2_validated = false
	player1_modified = false
	player2_modified = false
	player1_modification_type = ""
	player2_modification_type = ""

# Getters
func get_current_phase() -> GamePhase:
	return current_phase

func get_current_round() -> int:
	return current_round

func get_scores() -> Dictionary:
	return {"player1": player1_score, "player2": player2_score}

func is_player_validated(player_id: int) -> bool:
	return player1_validated if player_id == 1 else player2_validated

func get_player_selected_cards(player_id: int) -> Array:
	return player1_selected_cards if player_id == 1 else player2_selected_cards

# Future: Add special cards to hands
func add_special_cards_to_hands():
	# This will be implemented when special cards are added
	# For now, this is a placeholder
	pass
