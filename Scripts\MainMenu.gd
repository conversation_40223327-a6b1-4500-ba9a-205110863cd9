extends Control

@onready var main_layout = $MainLayout
@onready var mode_selection = $ModeSelection
@onready var multi_selection = $MultiSelection
@onready var private_room = $PrivateRoom

# Networking
var peer = ENetMultiplayerPeer.new()
var port = 8910
var address = "127.0.0.1"

# Scene paths
const CARD_GAME_SCENE = "res://Scenes/CardGame.tscn"
const MULTIPLAYER_LOBBY_SCENE = "res://Scenes/MultiplayerLobby.tscn"

func _ready():
    main_layout.modulate.a = 0
    var tween = create_tween()
    tween.tween_property(main_layout, "modulate:a", 1.0, 0.5)
    
    mode_selection.hide()
    multi_selection.hide()
    private_room.hide()

func _on_start_button_pressed() -> void:
    animate_panel_transition(mode_selection)

func _on_solo_button_pressed() -> void:
    get_tree().change_scene_to_file(CARD_GAME_SCENE)

func _on_multi_button_pressed() -> void:
    get_tree().change_scene_to_file(MULTIPLAYER_LOBBY_SCENE)

func _on_options_button_pressed() -> void:
    print("Options menu not implemented yet")

func _on_quit_button_pressed() -> void:
    get_tree().quit()

func _on_public_button_pressed() -> void:
    var matchmaking = preload("res://Scripts/Matchmaking.gd").new()
    add_child(matchmaking)
    matchmaking.start_matchmaking()

func _on_private_button_pressed() -> void:
    animate_panel_transition(private_room)

func animate_panel_transition(panel: Control) -> void:
    panel.modulate.a = 0
    panel.show()
    
    var tween = create_tween()
    tween.tween_property(panel, "modulate:a", 1.0, 0.3)
    tween.tween_property(panel, "scale", Vector2(1.1, 1.1), 0.1)
    tween.tween_property(panel, "scale", Vector2(1.0, 1.0), 0.1)

func _on_create_room_button_pressed() -> void:
    peer.create_server(port)
    multiplayer.multiplayer_peer = peer
    get_tree().change_scene_to_file(CARD_GAME_SCENE)

func _on_join_room_button_pressed() -> void:
    var room_code = $PrivateRoom/RoomCode.text
    if room_code.length() > 0:
        peer.create_client(address, port)
        multiplayer.multiplayer_peer = peer
        get_tree().change_scene_to_file(CARD_GAME_SCENE)
