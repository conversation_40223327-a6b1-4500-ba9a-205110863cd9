extends Control
class_name VictoryScreen

signal continue_pressed()
signal restart_pressed()

# UI Components
var background_panel: Panel
var main_container: VBoxContainer
var title_label: Label
var player1_section: VBoxContainer
var player2_section: VBoxContainer
var result_section: VBoxContainer
var button_section: HBoxContainer

# Data
var round_data: Dictionary

func _init():
	setup_ui()

func setup_ui():
	# Set full screen
	set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	
	# Semi-transparent background
	var background = ColorRect.new()
	background.color = Color(0, 0, 0, 0.7)
	background.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	add_child(background)
	
	# Main panel
	background_panel = Panel.new()
	background_panel.set_anchors_and_offsets_preset(Control.PRESET_CENTER)
	background_panel.size = Vector2(900, 700)
	background_panel.position = Vector2(-450, -350)
	
	# Style the panel
	var style_box = StyleBoxFlat.new()
	style_box.bg_color = Color(0.15, 0.15, 0.2, 0.95)
	style_box.border_width_left = 3
	style_box.border_width_right = 3
	style_box.border_width_top = 3
	style_box.border_width_bottom = 3
	style_box.border_color = Color(0.8, 0.6, 0.2, 1.0)
	style_box.corner_radius_top_left = 15
	style_box.corner_radius_top_right = 15
	style_box.corner_radius_bottom_left = 15
	style_box.corner_radius_bottom_right = 15
	background_panel.add_theme_stylebox_override("panel", style_box)
	
	add_child(background_panel)
	
	# Main container
	main_container = VBoxContainer.new()
	main_container.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	main_container.add_theme_constant_override("separation", 20)
	background_panel.add_child(main_container)
	
	create_title_section()
	create_player_sections()
	create_result_section()
	create_button_section()

func create_title_section():
	title_label = Label.new()
	title_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	title_label.add_theme_font_size_override("font_size", 32)
	title_label.add_theme_color_override("font_color", Color(1.0, 0.9, 0.3, 1.0))
	main_container.add_child(title_label)
	
	# Separator
	var separator = HSeparator.new()
	separator.add_theme_constant_override("separation", 10)
	main_container.add_child(separator)

func create_player_sections():
	# Players container
	var players_container = HBoxContainer.new()
	players_container.alignment = BoxContainer.ALIGNMENT_CENTER
	players_container.add_theme_constant_override("separation", 50)
	main_container.add_child(players_container)
	
	# Player 1 section
	player1_section = VBoxContainer.new()
	player1_section.add_theme_constant_override("separation", 15)
	players_container.add_child(player1_section)
	
	# VS label
	var vs_label = Label.new()
	vs_label.text = "⚔️\nVS\n⚔️"
	vs_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	vs_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	vs_label.add_theme_font_size_override("font_size", 24)
	vs_label.add_theme_color_override("font_color", Color(0.9, 0.9, 0.9, 1.0))
	players_container.add_child(vs_label)
	
	# Player 2 section
	player2_section = VBoxContainer.new()
	player2_section.add_theme_constant_override("separation", 15)
	players_container.add_child(player2_section)

func create_result_section():
	result_section = VBoxContainer.new()
	result_section.add_theme_constant_override("separation", 10)
	main_container.add_child(result_section)

func create_button_section():
	button_section = HBoxContainer.new()
	button_section.alignment = BoxContainer.ALIGNMENT_CENTER
	button_section.add_theme_constant_override("separation", 30)
	main_container.add_child(button_section)

func show_round_results(data: Dictionary):
	round_data = data
	
	# Update title
	var round_num = data.get("round", 1)
	title_label.text = "🎯 ROUND " + str(round_num) + " RESULTS 🎯"
	
	# Clear previous content
	clear_player_sections()
	clear_result_section()
	clear_button_section()
	
	# Populate player sections
	populate_player_section(player1_section, "👤 PLAYER 1", data.get("player1_cards", []), 
						   data.get("player1_hand", ""), data.get("player1_cheated", false))
	
	populate_player_section(player2_section, "🤖 PLAYER 2 (AI)", data.get("player2_cards", []), 
						   data.get("player2_hand", ""), data.get("player2_cheated", false))
	
	# Populate result section
	populate_result_section(data)
	
	# Populate button section
	populate_button_section(data)
	
	# Animate entrance
	animate_entrance()

func clear_player_sections():
	for child in player1_section.get_children():
		child.queue_free()
	for child in player2_section.get_children():
		child.queue_free()

func clear_result_section():
	for child in result_section.get_children():
		child.queue_free()

func clear_button_section():
	for child in button_section.get_children():
		child.queue_free()

func populate_player_section(section: VBoxContainer, title: String, cards: Array, hand_value: String, cheated: bool):
	# Player title
	var player_title = Label.new()
	player_title.text = title + (" (CHEATED ⚠️)" if cheated else " (LEGAL ✅)")
	player_title.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	player_title.add_theme_font_size_override("font_size", 20)
	player_title.add_theme_color_override("font_color", Color.RED if cheated else Color.GREEN)
	section.add_child(player_title)
	
	# Cards container
	var cards_container = HBoxContainer.new()
	cards_container.alignment = BoxContainer.ALIGNMENT_CENTER
	cards_container.add_theme_constant_override("separation", 5)
	section.add_child(cards_container)
	
	# Display cards
	for card in cards:
		var card_display = create_card_display(card)
		cards_container.add_child(card_display)
	
	# Hand value
	var hand_label = Label.new()
	hand_label.text = "Hand: " + hand_value
	hand_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	hand_label.add_theme_font_size_override("font_size", 16)
	hand_label.add_theme_color_override("font_color", Color(0.9, 0.9, 0.9, 1.0))
	section.add_child(hand_label)

func create_card_display(card: Dictionary) -> Control:
	var card_container = Control.new()
	card_container.custom_minimum_size = Vector2(60, 85)
	
	# Card background
	var card_bg = Panel.new()
	card_bg.size = Vector2(60, 85)
	
	var style = StyleBoxFlat.new()
	style.bg_color = Color.WHITE
	style.border_width_left = 2
	style.border_width_right = 2
	style.border_width_top = 2
	style.border_width_bottom = 2
	style.border_color = Color.RED if card.get("modified", false) else Color.BLACK
	style.corner_radius_top_left = 5
	style.corner_radius_top_right = 5
	style.corner_radius_bottom_left = 5
	style.corner_radius_bottom_right = 5
	card_bg.add_theme_stylebox_override("panel", style)
	
	card_container.add_child(card_bg)
	
	# Card text
	var card_label = Label.new()
	card_label.text = get_card_text(card)
	card_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	card_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	card_label.size = Vector2(60, 85)
	card_label.add_theme_font_size_override("font_size", 14)
	
	# Color based on suit
	var suit = card.get("suit", "hearts")
	if suit in ["hearts", "diamonds"]:
		card_label.add_theme_color_override("font_color", Color.RED)
	else:
		card_label.add_theme_color_override("font_color", Color.BLACK)
	
	card_container.add_child(card_label)
	
	return card_container

func get_card_text(card: Dictionary) -> String:
	var rank = card.get("value", 1)
	var suit = card.get("suit", "hearts")
	
	var rank_text = ""
	match rank:
		1: rank_text = "A"
		11: rank_text = "J"
		12: rank_text = "Q"
		13: rank_text = "K"
		_: rank_text = str(rank)
	
	var suit_symbol = ""
	match suit:
		"hearts": suit_symbol = "♥"
		"diamonds": suit_symbol = "♦"
		"clubs": suit_symbol = "♣"
		"spades": suit_symbol = "♠"
	
	return rank_text + "\n" + suit_symbol

func populate_result_section(data: Dictionary):
	# Winner announcement
	var winner_label = Label.new()
	var reason = data.get("reason", "")
	winner_label.text = reason
	winner_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	winner_label.add_theme_font_size_override("font_size", 24)
	
	var winner = data.get("winner", 0)
	if winner == 1:
		winner_label.add_theme_color_override("font_color", Color.GREEN)
	elif winner == 2:
		winner_label.add_theme_color_override("font_color", Color.BLUE)
	else:
		winner_label.add_theme_color_override("font_color", Color.YELLOW)
	
	result_section.add_child(winner_label)
	
	# Score
	var score_label = Label.new()
	var scores = data.get("scores", {"player1": 0, "player2": 0})
	score_label.text = "📊 SCORE: " + str(scores.player1) + " - " + str(scores.player2) + " 📊"
	score_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	score_label.add_theme_font_size_override("font_size", 20)
	score_label.add_theme_color_override("font_color", Color(1.0, 0.9, 0.3, 1.0))
	result_section.add_child(score_label)

func populate_button_section(data: Dictionary):
	var is_game_over = data.get("game_over", false)
	
	if is_game_over:
		# Game over - show restart button
		var restart_button = Button.new()
		restart_button.text = "🔄 Play Again"
		restart_button.custom_minimum_size = Vector2(200, 50)
		restart_button.add_theme_font_size_override("font_size", 18)
		restart_button.pressed.connect(_on_restart_pressed)
		button_section.add_child(restart_button)
	else:
		# Continue to next round
		var continue_button = Button.new()
		continue_button.text = "▶️ Next Round"
		continue_button.custom_minimum_size = Vector2(200, 50)
		continue_button.add_theme_font_size_override("font_size", 18)
		continue_button.pressed.connect(_on_continue_pressed)
		button_section.add_child(continue_button)

func animate_entrance():
	# Start with panel scaled down and transparent
	background_panel.scale = Vector2(0.5, 0.5)
	background_panel.modulate.a = 0.0
	
	# Animate entrance
	var tween = create_tween()
	tween.set_parallel(true)
	
	tween.tween_property(background_panel, "scale", Vector2(1.0, 1.0), 0.5)
	tween.tween_property(background_panel, "modulate:a", 1.0, 0.5)
	
	# Add a slight bounce
	await tween.finished
	var bounce_tween = create_tween()
	bounce_tween.tween_property(background_panel, "scale", Vector2(1.05, 1.05), 0.1)
	bounce_tween.tween_property(background_panel, "scale", Vector2(1.0, 1.0), 0.1)

func _on_continue_pressed():
	emit_signal("continue_pressed")
	queue_free()

func _on_restart_pressed():
	emit_signal("restart_pressed")
	queue_free()
