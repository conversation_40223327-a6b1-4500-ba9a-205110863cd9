[gd_scene load_steps=2 format=3 uid="uid://dxvn8ywqxqxqt"]

[ext_resource type="Script" path="res://Scripts/BluffRoyaleGame.gd" id="1_1a2b6"]

[node name="BluffRoyaleGame" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource("1_1a2b6")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
color = Color(0.1, 0.3, 0.1, 1)

[node name="MainContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = -20.0

[node name="Title" type="Label" parent="MainContainer"]
layout_mode = 2
text = "BLUFF ROYALE"
horizontal_alignment = 1
vertical_alignment = 1

[node name="PhaseLabel" type="Label" parent="MainContainer"]
layout_mode = 2
text = "CARD SELECTION"
horizontal_alignment = 1
vertical_alignment = 1

[node name="RoundScoreLabel" type="Label" parent="MainContainer"]
layout_mode = 2
text = "Round 1/3 | Score: 0 - 0"
horizontal_alignment = 1
vertical_alignment = 1

[node name="InstructionLabel" type="Label" parent="MainContainer"]
layout_mode = 2
text = "Select 1-5 cards from your hand"
horizontal_alignment = 1
vertical_alignment = 1

[node name="HSeparator" type="HSeparator" parent="MainContainer"]
layout_mode = 2

[node name="HandContainer" type="HBoxContainer" parent="MainContainer"]
layout_mode = 2
alignment = 1

[node name="HSeparator2" type="HSeparator" parent="MainContainer"]
layout_mode = 2

[node name="ButtonContainer" type="HBoxContainer" parent="MainContainer"]
layout_mode = 2
alignment = 1

[node name="HSeparator3" type="HSeparator" parent="MainContainer"]
layout_mode = 2

[node name="DebugLabel" type="Label" parent="MainContainer"]
layout_mode = 2
text = "Press Enter for debug info"
horizontal_alignment = 1
vertical_alignment = 1
