extends Node

signal matchmaking_complete(opponent_id: int)

var searching = false
var search_time = 0
var max_search_time = NetworkConfig.MAX_SEARCH_TIME
const MAX_RETRIES = NetworkConfig.MAX_RETRIES
var current_retry = 0

func _ready():
	# Initialize matchmaking server connection
	pass

func start_matchmaking():
	searching = true
	current_retry = 0
	show_searching_ui()
	_try_matchmaking()

func _try_matchmaking():
	if current_retry >= MAX_RETRIES:
		cancel_matchmaking()
		return
		
	# Existing matchmaking logic
	current_retry += 1

func _process(delta):
	if searching:
		search_time += delta
		update_searching_ui()
		
		if search_time >= max_search_time:
			cancel_matchmaking()

func search_for_opponent():
	# Simulate search for now
	var timer = get_tree().create_timer(randf_range(2.0, 5.0))
	timer.timeout.connect(func(): _on_opponent_found(randi()))

func _on_opponent_found(opponent_id: int):
	searching = false
	hide_searching_ui()
	emit_signal("matchmaking_complete", opponent_id)

func show_searching_ui():
	# Show search UI
	var ui = preload(NetworkConfig.SEARCHING_UI_SCENE).instantiate()
	add_child(ui)

func update_searching_ui():
	# Update UI with search time
	pass

func cancel_matchmaking():
	searching = false
	hide_searching_ui()
	# Return to main menu
	get_tree().change_scene_to_file(NetworkConfig.MAIN_MENU_SCENE)

func hide_searching_ui():
	# Hide search UI
	for child in get_children():
		if child.is_in_group("searching_ui"):
			child.queue_free()
