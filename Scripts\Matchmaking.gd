extends Node

signal matchmaking_complete(opponent_id: int)

var searching = false
var search_time = 0
var max_search_time = 30  # 30 secondes maximum de recherche
const MAX_RETRIES = 3
var current_retry = 0

func _ready():
	# Initialiser la connexion au serveur de matchmaking
	pass

func start_matchmaking():
	searching = true
	current_retry = 0
	show_searching_ui()
	_try_matchmaking()

func _try_matchmaking():
	if current_retry >= MAX_RETRIES:
		cancel_matchmaking()
		return
		
	# Logique de matchmaking existante
	current_retry += 1

func _process(delta):
	if searching:
		search_time += delta
		update_searching_ui()
		
		if search_time >= max_search_time:
			cancel_matchmaking()

func search_for_opponent():
	# Simulons une recherche pour l'instant
	var timer = get_tree().create_timer(randf_range(2.0, 5.0))
	timer.timeout.connect(func(): _on_opponent_found(randi()))

func _on_opponent_found(opponent_id: int):
	searching = false
	hide_searching_ui()
	emit_signal("matchmaking_complete", opponent_id)

func show_searching_ui():
	# Afficher l'UI de recherche
	var ui = preload("res://Scenes/SearchingUI.tscn").instantiate()
	add_child(ui)

func update_searching_ui():
	# Mettre à jour l'UI avec le temps de recherche
	pass

func cancel_matchmaking():
	searching = false
	hide_searching_ui()
	# Retourner au menu principal
	get_tree().change_scene_to_file("res://Scenes/MainMenu.tscn")

func hide_searching_ui():
	# Cacher l'UI de recherche
	for child in get_children():
		if child.is_in_group("searching_ui"):
			child.queue_free()
