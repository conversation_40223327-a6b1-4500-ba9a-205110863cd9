extends Control
class_name CardModificationUI

signal rank_selected(new_rank: int)
signal suit_selected(new_suit: String)
signal modification_cancelled()

var modification_panel: Panel
var rank_container: GridContainer
var suit_container: HBoxContainer

func _init():
	setup_ui()

func setup_ui():
	# Set full screen overlay
	set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	
	# Semi-transparent background
	var background = ColorRect.new()
	background.color = Color(0, 0, 0, 0.5)
	background.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	add_child(background)
	
	# Main panel
	modification_panel = Panel.new()
	modification_panel.set_anchors_and_offsets_preset(Control.PRESET_CENTER)
	modification_panel.size = Vector2(400, 300)
	modification_panel.position = Vector2(-200, -150)
	
	# Style the panel
	var style_box = StyleBoxFlat.new()
	style_box.bg_color = Color(0.2, 0.2, 0.3, 0.95)
	style_box.border_width_left = 2
	style_box.border_width_right = 2
	style_box.border_width_top = 2
	style_box.border_width_bottom = 2
	style_box.border_color = Color(0.8, 0.2, 0.2, 1.0)
	style_box.corner_radius_top_left = 10
	style_box.corner_radius_top_right = 10
	style_box.corner_radius_bottom_left = 10
	style_box.corner_radius_bottom_right = 10
	modification_panel.add_theme_stylebox_override("panel", style_box)
	
	add_child(modification_panel)
	
	# Main container
	var main_container = VBoxContainer.new()
	main_container.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	main_container.add_theme_constant_override("separation", 20)
	modification_panel.add_child(main_container)
	
	# Title
	var title = Label.new()
	title.text = "⚠️ ILLEGAL MODIFICATION ⚠️"
	title.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	title.add_theme_font_size_override("font_size", 20)
	title.add_theme_color_override("font_color", Color.RED)
	main_container.add_child(title)
	
	# Warning
	var warning = Label.new()
	warning.text = "This action will mark you as a cheater!"
	warning.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	warning.add_theme_font_size_override("font_size", 14)
	warning.add_theme_color_override("font_color", Color.YELLOW)
	main_container.add_child(warning)

func show_rank_modification():
	clear_content()
	
	var content_container = VBoxContainer.new()
	content_container.add_theme_constant_override("separation", 15)
	modification_panel.add_child(content_container)
	
	# Title
	var title = Label.new()
	title.text = "Select New Rank:"
	title.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	title.add_theme_font_size_override("font_size", 16)
	content_container.add_child(title)
	
	# Rank buttons
	rank_container = GridContainer.new()
	rank_container.columns = 4
	rank_container.add_theme_constant_override("h_separation", 10)
	rank_container.add_theme_constant_override("v_separation", 10)
	content_container.add_child(rank_container)
	
	# Create rank buttons (A, 2-10, J, Q, K)
	var ranks = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13]
	var rank_names = ["A", "2", "3", "4", "5", "6", "7", "8", "9", "10", "J", "Q", "K"]
	
	for i in range(ranks.size()):
		var rank_button = Button.new()
		rank_button.text = rank_names[i]
		rank_button.custom_minimum_size = Vector2(50, 40)
		rank_button.pressed.connect(func(): _on_rank_selected(ranks[i]))
		rank_container.add_child(rank_button)
	
	# Cancel button
	add_cancel_button(content_container)

func show_suit_modification():
	clear_content()
	
	var content_container = VBoxContainer.new()
	content_container.add_theme_constant_override("separation", 15)
	modification_panel.add_child(content_container)
	
	# Title
	var title = Label.new()
	title.text = "Select New Suit:"
	title.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	title.add_theme_font_size_override("font_size", 16)
	content_container.add_child(title)
	
	# Suit buttons
	suit_container = HBoxContainer.new()
	suit_container.alignment = BoxContainer.ALIGNMENT_CENTER
	suit_container.add_theme_constant_override("separation", 15)
	content_container.add_child(suit_container)
	
	# Create suit buttons
	var suits = ["hearts", "diamonds", "clubs", "spades"]
	var suit_symbols = ["♥", "♦", "♣", "♠"]
	var suit_colors = [Color.RED, Color.RED, Color.BLACK, Color.BLACK]
	
	for i in range(suits.size()):
		var suit_button = Button.new()
		suit_button.text = suit_symbols[i]
		suit_button.custom_minimum_size = Vector2(80, 60)
		suit_button.add_theme_font_size_override("font_size", 24)
		suit_button.add_theme_color_override("font_color", suit_colors[i])
		suit_button.pressed.connect(func(): _on_suit_selected(suits[i]))
		suit_container.add_child(suit_button)
	
	# Cancel button
	add_cancel_button(content_container)

func add_cancel_button(container: VBoxContainer):
	var button_container = HBoxContainer.new()
	button_container.alignment = BoxContainer.ALIGNMENT_CENTER
	container.add_child(button_container)
	
	var cancel_button = Button.new()
	cancel_button.text = "Cancel"
	cancel_button.custom_minimum_size = Vector2(100, 40)
	cancel_button.pressed.connect(_on_cancel_pressed)
	button_container.add_child(cancel_button)

func clear_content():
	# Remove all children except the first two (title and warning)
	var children = modification_panel.get_children()
	for i in range(1, children.size()):
		children[i].queue_free()

func _on_rank_selected(rank: int):
	emit_signal("rank_selected", rank)
	queue_free()

func _on_suit_selected(suit: String):
	emit_signal("suit_selected", suit)
	queue_free()

func _on_cancel_pressed():
	emit_signal("modification_cancelled")
	queue_free()

func animate_entrance():
	# Start with panel scaled down
	modification_panel.scale = Vector2(0.5, 0.5)
	modification_panel.modulate.a = 0.0
	
	# Animate entrance
	var tween = create_tween()
	tween.set_parallel(true)
	
	tween.tween_property(modification_panel, "scale", Vector2(1.0, 1.0), 0.3)
	tween.tween_property(modification_panel, "modulate:a", 1.0, 0.3)
