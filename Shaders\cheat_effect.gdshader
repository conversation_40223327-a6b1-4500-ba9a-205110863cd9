shader_type canvas_item;

uniform float distortion_amount : hint_range(0.0, 1.0) = 0.2;
uniform float glitch_intensity : hint_range(0.0, 1.0) = 0.1;

void fragment() {
    vec2 uv = UV;
    
    // Effet de distorsion
    float distortion = sin(uv.y * 50.0 + TIME * 5.0) * distortion_amount;
    uv.x += distortion;
    
    // Effet de glitch
    float glitch = step(0.98, sin(TIME * 10.0)) * glitch_intensity;
    uv.x += glitch;
    
    // Effet de chromatic aberration
    vec4 red = texture(TEXTURE, uv + vec2(0.01, 0.0) * distortion_amount);
    vec4 green = texture(TEXTURE, uv);
    vec4 blue = texture(TEXTURE, uv - vec2(0.01, 0.0) * distortion_amount);
    
    COLOR = vec4(red.r, green.g, blue.b, texture(TEXTURE, UV).a);
}