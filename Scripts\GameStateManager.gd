extends Node
class_name GameStateManager

signal game_state_changed(new_state: String)
signal round_started(round_number: int)
signal round_ended(winner: int, results: Dictionary)
signal game_ended(final_winner: int, final_scores: Dictionary)

# Game states
enum GameState {
	SETUP,
	HAND_PREPARATION,  # Both players prepare simultaneously
	HAND_REVEAL,       # Show hands and determine winner
	ROUND_END,         # Show round results
	GAME_END          # Show final results
}

# Game configuration
const MAX_ROUNDS = 3
const CARDS_PER_HAND = 8
const MAX_SELECTED_CARDS = 5

# Current game state
var current_state: GameState = GameState.SETUP
var current_round: int = 1
var player1_score: int = 0
var player2_score: int = 0

# Round state
var player1_ready: bool = false
var player2_ready: bool = false
var player1_hand_data: Dictionary = {}
var player2_hand_data: Dictionary = {}

# Cheat tracking (hidden until reveal)
var player1_cheated: bool = false
var player2_cheated: bool = false
var player1_cheat_type: String = ""
var player2_cheat_type: String = ""

func _ready():
	pass

# Start a new game
func start_new_game():
	current_state = GameState.SETUP
	current_round = 1
	player1_score = 0
	player2_score = 0
	reset_round_state()
	emit_signal("game_state_changed", "setup")

# Start a new round
func start_round():
	if current_state != GameState.SETUP and current_state != GameState.ROUND_END:
		push_warning("Cannot start round from current state")
		return
	
	reset_round_state()
	current_state = GameState.HAND_PREPARATION
	emit_signal("round_started", current_round)
	emit_signal("game_state_changed", "hand_preparation")

# Player submits their hand
func submit_player_hand(player_id: int, selected_cards: Array, cheated: bool = false, cheat_type: String = ""):
	if current_state != GameState.HAND_PREPARATION:
		push_warning("Cannot submit hand in current state")
		return false
	
	if player_id == 1:
		player1_hand_data = {
			"cards": selected_cards.duplicate(),
			"submitted_at": Time.get_unix_time_from_system()
		}
		player1_ready = true
		player1_cheated = cheated
		player1_cheat_type = cheat_type
	elif player_id == 2:
		player2_hand_data = {
			"cards": selected_cards.duplicate(),
			"submitted_at": Time.get_unix_time_from_system()
		}
		player2_ready = true
		player2_cheated = cheated
		player2_cheat_type = cheat_type
	else:
		push_error("Invalid player ID: " + str(player_id))
		return false
	
	# Check if both players are ready
	if player1_ready and player2_ready:
		reveal_hands()
	
	return true

# Reveal both hands and determine winner
func reveal_hands():
	if current_state != GameState.HAND_PREPARATION:
		push_warning("Cannot reveal hands in current state")
		return
	
	current_state = GameState.HAND_REVEAL
	
	# Determine round winner
	var results = determine_round_winner()
	
	# Update scores
	if results.winner == 1:
		player1_score += 1
	elif results.winner == 2:
		player2_score += 1
	
	emit_signal("round_ended", results.winner, results)
	
	# Check if game is over
	if current_round >= MAX_ROUNDS or player1_score > MAX_ROUNDS/2 or player2_score > MAX_ROUNDS/2:
		end_game()
	else:
		current_state = GameState.ROUND_END
		emit_signal("game_state_changed", "round_end")

# Determine the winner of the current round
func determine_round_winner() -> Dictionary:
	var poker_evaluator = load("res://Scripts/PokerHand.gd").new()
	
	# Convert card data for evaluation
	var p1_eval_cards = CardUtils.convert_cards_for_evaluation(player1_hand_data.cards)
	var p2_eval_cards = CardUtils.convert_cards_for_evaluation(player2_hand_data.cards)
	
	# Evaluate hands
	var p1_hand_value = poker_evaluator.evaluate_hand(p1_eval_cards)
	var p2_hand_value = poker_evaluator.evaluate_hand(p2_eval_cards)
	
	# Get hand scores for comparison
	var hand_manager = load("res://Scripts/HandManager.gd").new()
	var p1_score = hand_manager.get_hand_score(p1_hand_value)
	var p2_score = hand_manager.get_hand_score(p2_hand_value)
	
	var winner = 0
	var reason = ""
	
	# Apply cheat rules
	if player1_cheated and not player2_cheated:
		winner = 2
		reason = "Player 1 cheated, Player 2 wins!"
	elif player2_cheated and not player1_cheated:
		winner = 1
		reason = "Player 2 cheated, Player 1 wins!"
	elif player1_cheated and player2_cheated:
		# Both cheated, compare hands normally
		if p1_score > p2_score:
			winner = 1
			reason = "Both cheated - Player 1 wins with " + p1_hand_value
		elif p2_score > p1_score:
			winner = 2
			reason = "Both cheated - Player 2 wins with " + p2_hand_value
		else:
			winner = 0
			reason = "Both cheated - Tie with " + p1_hand_value
	else:
		# No cheating, compare hands normally
		if p1_score > p2_score:
			winner = 1
			reason = "Player 1 wins with " + p1_hand_value + " vs " + p2_hand_value
		elif p2_score > p1_score:
			winner = 2
			reason = "Player 2 wins with " + p2_hand_value + " vs " + p1_hand_value
		else:
			winner = 0
			reason = "Tie with " + p1_hand_value
	
	return {
		"winner": winner,
		"reason": reason,
		"player1_hand": p1_hand_value,
		"player2_hand": p2_hand_value,
		"player1_cheated": player1_cheated,
		"player2_cheated": player2_cheated,
		"player1_cheat_type": player1_cheat_type,
		"player2_cheat_type": player2_cheat_type
	}

# End the current game
func end_game():
	current_state = GameState.GAME_END
	var final_winner = 1 if player1_score > player2_score else (2 if player2_score > player1_score else 0)
	var final_scores = {"player1": player1_score, "player2": player2_score}
	emit_signal("game_ended", final_winner, final_scores)
	emit_signal("game_state_changed", "game_end")

# Prepare for next round
func next_round():
	if current_state != GameState.ROUND_END:
		push_warning("Cannot advance to next round from current state")
		return
	
	current_round += 1
	start_round()

# Reset round-specific state
func reset_round_state():
	player1_ready = false
	player2_ready = false
	player1_hand_data.clear()
	player2_hand_data.clear()
	player1_cheated = false
	player2_cheated = false
	player1_cheat_type = ""
	player2_cheat_type = ""

# Getters
func get_current_state() -> GameState:
	return current_state

func get_current_round() -> int:
	return current_round

func get_scores() -> Dictionary:
	return {"player1": player1_score, "player2": player2_score}

func is_player_ready(player_id: int) -> bool:
	return player1_ready if player_id == 1 else player2_ready

func both_players_ready() -> bool:
	return player1_ready and player2_ready
