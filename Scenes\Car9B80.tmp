[gd_scene load_steps=2 format=3 uid="uid://cil6aa3jpw4oq"]

[ext_resource type="Script" path="res://Scripts/CardGame.gd" id="1_yn0qs"]

[node name="Root" type="Control"]
z_index = 1
layout_mode = 3
anchors_preset = 0
offset_right = 1024.0
offset_bottom = 768.0
script = ExtResource("1_yn0qs")

[node name="MainLayout" type="VBoxContainer" parent="."]
layout_mode = 0
offset_left = 22.0
offset_top = 518.0
offset_right = 984.0
offset_bottom = 756.0

[node name="CardHand" type="HBoxContainer" parent="MainLayout"]
layout_mode = 2

[node name="Card1" type="TextureButton" parent="MainLayout/CardHand"]
layout_mode = 2

[node name="Card2" type="TextureButton" parent="MainLayout/CardHand"]
layout_mode = 2

[node name="Card3" type="TextureButton" parent="MainLayout/CardHand"]
layout_mode = 2

[node name="Card4" type="TextureButton" parent="MainLayout/CardHand"]
layout_mode = 2

[node name="Card5" type="TextureButton" parent="MainLayout/CardHand"]
layout_mode = 2

[node name="Card6" type="TextureButton" parent="MainLayout/CardHand"]
layout_mode = 2

[node name="Card7" type="TextureButton" parent="MainLayout/CardHand"]
layout_mode = 2

[node name="Card8" type="TextureButton" parent="MainLayout/CardHand"]
layout_mode = 2

[node name="PokerHandLabel" type="Label" parent="MainLayout"]
layout_mode = 2

[node name="ConfirmButton" type="Button" parent="MainLayout"]
layout_mode = 2
text = "Confirmer"

[node name="MenuButton" type="Button" parent="."]
layout_mode = 0
offset_left = 839.0
offset_top = 8.0
offset_right = 1014.0
offset_bottom = 88.0
text = "Retour au menu"

[node name="BestHand" type="Button" parent="."]
layout_mode = 0
offset_left = 20.0
offset_top = 440.0
offset_right = 159.0
offset_bottom = 498.0
text = "Meilleur Main"

[connection signal="gui_input" from="." to="." method="_on_gui_input"]
[connection signal="sort_children" from="MainLayout/CardHand" to="." method="_on_card_hand_sort_children"]
[connection signal="item_rect_changed" from="MainLayout/CardHand/Card1" to="." method="_on_card_1_item_rect_changed"]
[connection signal="pressed" from="MenuButton" to="." method="_on_menu_button_pressed"]
[connection signal="pressed" from="BestHand" to="." method="_on_best_hand_pressed"]
