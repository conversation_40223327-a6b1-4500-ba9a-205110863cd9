extends Node
class_name SimpleBestOfThree

signal phase_changed(phase_name: String, instructions: String)
signal round_ended(winner: int, results: Dictionary)
signal game_ended(final_winner: int, scores: Dictionary)

# Game phases - simple and sequential
enum Phase {
	CARD_SELECTION,    # Both players select cards
	MODIFICATION,      # Both players can modify (optional)
	VALIDATION,        # Both players validate
	REVEAL            # Show results
}

# Game state
var current_phase: Phase = Phase.CARD_SELECTION
var current_round: int = 1
var player1_score: int = 0
var player2_score: int = 0

# Round state
var player1_selected_cards: Array = []
var player2_selected_cards: Array = []
var player1_has_proceeded: bool = false
var player2_has_proceeded: bool = false
var player1_has_modified: bool = false
var player2_has_modified: bool = false
var player1_modification_type: String = ""  # "", "exchange", "illegal"
var player2_modification_type: String = ""
var player1_validated: bool = false
var player2_validated: bool = false

# Managers
var hand_manager: HandManager

func _ready():
	pass

func set_hand_manager(manager: HandManager):
	hand_manager = manager

# Start new game
func start_new_game():
	current_round = 1
	player1_score = 0
	player2_score = 0
	start_round()

# Start new round
func start_round():
	reset_round_state()
	current_phase = Phase.CARD_SELECTION
	
	# Generate new hands
	if hand_manager:
		hand_manager.initialize_solo_game()
	
	emit_signal("phase_changed", "CARD SELECTION", "Select 1-5 cards from your hand, then click Proceed")
	print("=== ROUND ", current_round, " STARTED ===")
	print("Phase: Card Selection")

# Reset all round state
func reset_round_state():
	player1_selected_cards.clear()
	player2_selected_cards.clear()
	player1_has_proceeded = false
	player2_has_proceeded = false
	player1_has_modified = false
	player2_has_modified = false
	player1_modification_type = ""
	player2_modification_type = ""
	player1_validated = false
	player2_validated = false

# PHASE 1: Card Selection
func select_cards(player_id: int, card_ids: Array) -> bool:
	if current_phase != Phase.CARD_SELECTION:
		print("Not in card selection phase")
		return false
	
	if card_ids.size() < 1 or card_ids.size() > 5:
		print("Must select 1-5 cards")
		return false
	
	if player_id == 1:
		player1_selected_cards = card_ids.duplicate()
		print("Player 1 selected ", card_ids.size(), " cards")
	elif player_id == 2:
		player2_selected_cards = card_ids.duplicate()
		print("Player 2 selected ", card_ids.size(), " cards")
	
	return true

func proceed_from_selection(player_id: int) -> bool:
	if current_phase != Phase.CARD_SELECTION:
		return false
	
	var selected_cards = player1_selected_cards if player_id == 1 else player2_selected_cards
	if selected_cards.is_empty():
		print("Player ", player_id, " must select cards first")
		return false
	
	if player_id == 1:
		player1_has_proceeded = true
		print("Player 1 proceeded to modification phase")
	else:
		player2_has_proceeded = true
		print("Player 2 proceeded to modification phase")
	
	# Check if both players have proceeded
	if player1_has_proceeded and player2_has_proceeded:
		advance_to_modification()
	
	return true

func advance_to_modification():
	current_phase = Phase.MODIFICATION
	emit_signal("phase_changed", "MODIFICATION", "Exchange a card (legal), modify a card (illegal), or validate directly")
	print("Phase: Modification")

# PHASE 2: Modification (Optional)
func exchange_card(player_id: int, old_card_id: int, new_card_id: int) -> bool:
	if current_phase != Phase.MODIFICATION:
		print("Not in modification phase")
		return false
	
	if (player_id == 1 and player1_has_modified) or (player_id == 2 and player2_has_modified):
		print("Player ", player_id, " has already modified")
		return false
	
	var selected_cards = player1_selected_cards if player_id == 1 else player2_selected_cards
	var player_hand = hand_manager.player1_hand if player_id == 1 else hand_manager.player2_hand
	
	# Validate exchange
	if not selected_cards.has(old_card_id):
		print("Old card not in selection")
		return false
	
	var new_card_available = false
	for card in player_hand:
		if card.id == new_card_id and not selected_cards.has(new_card_id):
			new_card_available = true
			break
	
	if not new_card_available:
		print("New card not available")
		return false
	
	# Perform exchange
	var index = selected_cards.find(old_card_id)
	selected_cards[index] = new_card_id
	
	# Update state
	if player_id == 1:
		player1_selected_cards = selected_cards
		player1_has_modified = true
		player1_modification_type = "exchange"
		print("Player 1 exchanged card (LEGAL)")
	else:
		player2_selected_cards = selected_cards
		player2_has_modified = true
		player2_modification_type = "exchange"
		print("Player 2 exchanged card (LEGAL)")
	
	return true

func modify_card(player_id: int, card_id: int, modification_type: String, new_value) -> bool:
	if current_phase != Phase.MODIFICATION:
		print("Not in modification phase")
		return false
	
	if (player_id == 1 and player1_has_modified) or (player_id == 2 and player2_has_modified):
		print("Player ", player_id, " has already modified")
		return false
	
	var selected_cards = player1_selected_cards if player_id == 1 else player2_selected_cards
	
	if not selected_cards.has(card_id):
		print("Card not in selection")
		return false
	
	# Apply modification
	if modification_type == "rank":
		hand_manager.modify_card_rank(card_id, new_value)
	elif modification_type == "suit":
		hand_manager.modify_card_suit(card_id, new_value)
	else:
		return false
	
	# Update state
	if player_id == 1:
		player1_has_modified = true
		player1_modification_type = "illegal"
		print("Player 1 modified card (ILLEGAL)")
	else:
		player2_has_modified = true
		player2_modification_type = "illegal"
		print("Player 2 modified card (ILLEGAL)")
	
	return true

# PHASE 3: Validation
func validate_hand(player_id: int) -> bool:
	if current_phase != Phase.MODIFICATION and current_phase != Phase.VALIDATION:
		print("Cannot validate in current phase")
		return false
	
	var selected_cards = player1_selected_cards if player_id == 1 else player2_selected_cards
	if selected_cards.is_empty():
		print("Player ", player_id, " has no cards selected")
		return false
	
	if player_id == 1:
		player1_validated = true
		print("Player 1 VALIDATED hand")
	else:
		player2_validated = true
		print("Player 2 VALIDATED hand")
	
	# Check if both validated
	if player1_validated and player2_validated:
		advance_to_reveal()
	elif current_phase == Phase.MODIFICATION:
		current_phase = Phase.VALIDATION
		emit_signal("phase_changed", "VALIDATION", "Waiting for other player to validate...")
		print("Phase: Validation - Waiting for other player")
	
	return true

# PHASE 4: Reveal
func advance_to_reveal():
	current_phase = Phase.REVEAL
	emit_signal("phase_changed", "REVEAL", "Revealing hands...")
	print("Phase: Reveal")
	
	# Determine winner
	var results = determine_winner()
	
	# Update scores
	if results.winner == 1:
		player1_score += 1
	elif results.winner == 2:
		player2_score += 1
	
	print("Round ", current_round, " winner: Player ", results.winner)
	print("Score: ", player1_score, " - ", player2_score)
	
	emit_signal("round_ended", results.winner, results)
	
	# Check game end
	if player1_score >= 2 or player2_score >= 2 or current_round >= 3:
		end_game()
	else:
		# Prepare next round
		current_round += 1

func determine_winner() -> Dictionary:
	# Get card info
	var p1_cards = hand_manager.get_cards_info(player1_selected_cards)
	var p2_cards = hand_manager.get_cards_info(player2_selected_cards)
	
	# Evaluate hands
	var p1_hand_value = hand_manager.evaluate_hand(p1_cards)
	var p2_hand_value = hand_manager.evaluate_hand(p2_cards)
	var p1_score = hand_manager.get_hand_score(p1_hand_value)
	var p2_score = hand_manager.get_hand_score(p2_hand_value)
	
	# Apply rules
	var p1_cheated = player1_modification_type == "illegal"
	var p2_cheated = player2_modification_type == "illegal"
	
	var winner = 0
	var reason = ""
	
	if p1_cheated and not p2_cheated:
		winner = 2
		reason = "Player 1 cheated - Player 2 wins!"
	elif p2_cheated and not p1_cheated:
		winner = 1
		reason = "Player 2 cheated - Player 1 wins!"
	elif p1_cheated and p2_cheated:
		if p1_score > p2_score:
			winner = 1
			reason = "Both cheated - Player 1 wins with " + p1_hand_value
		elif p2_score > p1_score:
			winner = 2
			reason = "Both cheated - Player 2 wins with " + p2_hand_value
		else:
			winner = 0
			reason = "Both cheated - Tie"
	else:
		if p1_score > p2_score:
			winner = 1
			reason = "Player 1 wins: " + p1_hand_value + " vs " + p2_hand_value
		elif p2_score > p1_score:
			winner = 2
			reason = "Player 2 wins: " + p2_hand_value + " vs " + p1_hand_value
		else:
			winner = 0
			reason = "Tie: " + p1_hand_value
	
	return {
		"winner": winner,
		"reason": reason,
		"player1_hand": p1_hand_value,
		"player2_hand": p2_hand_value,
		"player1_cards": p1_cards,
		"player2_cards": p2_cards,
		"player1_cheated": p1_cheated,
		"player2_cheated": p2_cheated
	}

func next_round():
	start_round()

func end_game():
	var final_winner = 1 if player1_score > player2_score else (2 if player2_score > player1_score else 0)
	var scores = {"player1": player1_score, "player2": player2_score}
	emit_signal("game_ended", final_winner, scores)
	print("=== GAME ENDED ===")
	print("Final winner: Player ", final_winner)
	print("Final scores: ", scores)

# Getters
func get_current_phase() -> Phase:
	return current_phase

func get_current_round() -> int:
	return current_round

func get_scores() -> Dictionary:
	return {"player1": player1_score, "player2": player2_score}

func get_selected_cards(player_id: int) -> Array:
	return player1_selected_cards if player_id == 1 else player2_selected_cards

func has_player_proceeded(player_id: int) -> bool:
	return player1_has_proceeded if player_id == 1 else player2_has_proceeded

func has_player_modified(player_id: int) -> bool:
	return player1_has_modified if player_id == 1 else player2_has_modified

func has_player_validated(player_id: int) -> bool:
	return player1_validated if player_id == 1 else player2_validated
