extends Control
class_name SimultaneousGameUI

signal hand_submitted(selected_cards: Array, cheated: bool, cheat_type: String)

# UI References
@onready var hand_container: Control = $HandContainer
@onready var selected_cards_display: Control = $SelectedCardsDisplay
@onready var action_buttons: Control = $ActionButtons
@onready var status_label: Label = $StatusLabel
@onready var round_info: Label = $RoundInfo
@onready var score_display: Label = $ScoreDisplay

# Action buttons
@onready var select_best_button: Button = $ActionButtons/SelectBestButton
@onready var cheat_rank_button: Button = $ActionButtons/CheatRankButton
@onready var cheat_suit_button: Button = $ActionButtons/CheatSuitButton
@onready var submit_hand_button: Button = $ActionButtons/SubmitHandButton
@onready var clear_selection_button: Button = $ActionButtons/ClearSelectionButton

# Cheat panels
@onready var cheat_rank_panel: Panel = $CheatRankPanel
@onready var cheat_suit_panel: Panel = $CheatSuitPanel

# Game state
var current_hand: Array = []
var selected_card_ids: Array = []
var max_selected_cards: int = 5
var has_cheated: bool = false
var cheat_type: String = ""
var is_hand_submitted: bool = false

# Managers
var hand_manager: HandManager
var game_state_manager: GameStateManager

func _ready():
	setup_ui_connections()
	update_ui_state()

func setup_ui_connections():
	select_best_button.pressed.connect(_on_select_best_pressed)
	cheat_rank_button.pressed.connect(_on_cheat_rank_pressed)
	cheat_suit_button.pressed.connect(_on_cheat_suit_pressed)
	submit_hand_button.pressed.connect(_on_submit_hand_pressed)
	clear_selection_button.pressed.connect(_on_clear_selection_pressed)

func set_managers(hand_mgr: HandManager, state_mgr: GameStateManager):
	hand_manager = hand_mgr
	game_state_manager = state_mgr
	
	# Connect to game state changes
	game_state_manager.game_state_changed.connect(_on_game_state_changed)
	game_state_manager.round_started.connect(_on_round_started)

func _on_game_state_changed(new_state: String):
	update_ui_state()

func _on_round_started(round_number: int):
	reset_for_new_round()
	update_round_info()

func reset_for_new_round():
	selected_card_ids.clear()
	has_cheated = false
	cheat_type = ""
	is_hand_submitted = false
	cheat_rank_panel.visible = false
	cheat_suit_panel.visible = false
	update_ui_state()

func update_round_info():
	if game_state_manager:
		var round_num = game_state_manager.get_current_round()
		var scores = game_state_manager.get_scores()
		round_info.text = "Round " + str(round_num) + "/3"
		score_display.text = "Score: " + str(scores.player1) + " - " + str(scores.player2)

func update_ui_state():
	if not game_state_manager:
		return
	
	var current_state = game_state_manager.get_current_state()
	
	match current_state:
		GameStateManager.GameState.HAND_PREPARATION:
			enable_hand_preparation_ui()
		GameStateManager.GameState.HAND_REVEAL:
			disable_all_interactions()
			status_label.text = "Revealing hands..."
		GameStateManager.GameState.ROUND_END:
			disable_all_interactions()
			status_label.text = "Round ended"
		_:
			disable_all_interactions()

func enable_hand_preparation_ui():
	if is_hand_submitted:
		disable_all_interactions()
		status_label.text = "Hand submitted - Waiting for opponent..."
		return
	
	# Enable action buttons based on current state
	select_best_button.disabled = false
	clear_selection_button.disabled = selected_card_ids.is_empty()
	submit_hand_button.disabled = selected_card_ids.is_empty()
	
	# Cheat buttons - can only use once per round
	cheat_rank_button.disabled = has_cheated
	cheat_suit_button.disabled = has_cheated
	
	# Update status
	if selected_card_ids.is_empty():
		status_label.text = "Select cards for your hand (max " + str(max_selected_cards) + ")"
	else:
		status_label.text = str(selected_card_ids.size()) + "/" + str(max_selected_cards) + " cards selected"

func disable_all_interactions():
	select_best_button.disabled = true
	cheat_rank_button.disabled = true
	cheat_suit_button.disabled = true
	submit_hand_button.disabled = true
	clear_selection_button.disabled = true

# Button handlers
func _on_select_best_pressed():
	if hand_manager:
		hand_manager.clear_selection()
		hand_manager.select_best_hand()
		selected_card_ids = hand_manager.get_selected_cards()
		update_ui_state()

func _on_cheat_rank_pressed():
	if selected_card_ids.size() != 1:
		show_message("Select exactly one card to modify its rank")
		return
	
	show_cheat_rank_panel()

func _on_cheat_suit_pressed():
	if selected_card_ids.size() != 1:
		show_message("Select exactly one card to modify its suit")
		return
	
	show_cheat_suit_panel()

func _on_submit_hand_pressed():
	if selected_card_ids.is_empty():
		show_message("You must select at least one card")
		return
	
	if selected_card_ids.size() > max_selected_cards:
		show_message("Too many cards selected (max " + str(max_selected_cards) + ")")
		return
	
	# Get card information
	var selected_cards_info = []
	if hand_manager:
		selected_cards_info = hand_manager.get_cards_info(selected_card_ids)
	
	# Submit the hand
	is_hand_submitted = true
	emit_signal("hand_submitted", selected_cards_info, has_cheated, cheat_type)
	update_ui_state()

func _on_clear_selection_pressed():
	if hand_manager:
		hand_manager.clear_selection()
	selected_card_ids.clear()
	update_ui_state()

func show_cheat_rank_panel():
	cheat_rank_panel.visible = true
	cheat_suit_panel.visible = false
	# TODO: Populate with rank options

func show_cheat_suit_panel():
	cheat_suit_panel.visible = true
	cheat_rank_panel.visible = false
	# TODO: Populate with suit options

func apply_cheat(type: String, card_id: int, new_value):
	if has_cheated:
		show_message("You can only cheat once per round")
		return false
	
	has_cheated = true
	cheat_type = type
	
	# Apply the cheat through hand manager
	if hand_manager:
		if type == "rank":
			hand_manager.modify_card_rank(card_id, new_value)
		elif type == "suit":
			hand_manager.modify_card_suit(card_id, new_value)
	
	# Hide cheat panels
	cheat_rank_panel.visible = false
	cheat_suit_panel.visible = false
	
	update_ui_state()
	show_message("Cheat applied! (" + type + ")")
	return true

func show_message(text: String):
	# TODO: Implement message display
	print("UI Message: " + text)

func update_selected_cards_display():
	# TODO: Update visual display of selected cards
	pass

# Card selection handling
func on_card_selected(card_id: int):
	if is_hand_submitted:
		return
	
	if selected_card_ids.has(card_id):
		selected_card_ids.erase(card_id)
	else:
		if selected_card_ids.size() < max_selected_cards:
			selected_card_ids.append(card_id)
		else:
			show_message("Maximum " + str(max_selected_cards) + " cards allowed")
			return
	
	update_selected_cards_display()
	update_ui_state()

func get_selected_cards() -> Array:
	return selected_card_ids.duplicate()
