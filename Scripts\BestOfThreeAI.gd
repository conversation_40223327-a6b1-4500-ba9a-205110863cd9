extends Node
class_name BestOf<PERSON>hreeAI

# AI difficulty and behavior
enum Difficulty {
	EASY,    # 20% cheat chance, basic decisions
	MEDIUM,  # 40% cheat chance, good decisions
	HARD     # 60% cheat chance, optimal decisions
}

var difficulty: Difficulty = Difficulty.MEDIUM
var best_of_three_manager: BestOfThreeManager
var hand_manager: HandManager

# AI decision parameters
var cheat_probability: float = 0.4
var exchange_probability: float = 0.3
var risk_tolerance: float = 0.6

# AI state
var has_made_decision: bool = false
var selected_cards: Array = []
var decision_timer: Timer

func _init(diff: Difficulty = Difficulty.MEDIUM):
	difficulty = diff
	set_difficulty_parameters()

func set_difficulty_parameters():
	match difficulty:
		Difficulty.EASY:
			cheat_probability = 0.2
			exchange_probability = 0.4
			risk_tolerance = 0.3
		Difficulty.MEDIUM:
			cheat_probability = 0.4
			exchange_probability = 0.3
			risk_tolerance = 0.6
		Difficulty.HARD:
			cheat_probability = 0.6
			exchange_probability = 0.2
			risk_tolerance = 0.8

func _ready():
	# Create timer for AI decisions
	decision_timer = Timer.new()
	decision_timer.wait_time = randf_range(1.0, 3.0)
	decision_timer.one_shot = true
	decision_timer.timeout.connect(_on_decision_timer_timeout)
	add_child(decision_timer)

func set_managers(bot_manager: BestOfThreeManager, hand_mgr: HandManager):
	best_of_three_manager = bot_manager
	hand_manager = hand_mgr
	
	# Connect to phase changes
	best_of_three_manager.phase_changed.connect(_on_phase_changed)
	best_of_three_manager.round_started.connect(_on_round_started)

func _on_round_started(round_number: int):
	has_made_decision = false
	selected_cards.clear()
	print("AI: New round started")

func _on_phase_changed(new_phase: String):
	match new_phase:
		"card_selection":
			handle_card_selection_phase()
		"modification":
			handle_modification_phase()

func handle_card_selection_phase():
	print("AI: Card selection phase")
	# Start decision timer
	decision_timer.wait_time = randf_range(1.5, 3.0)
	decision_timer.start()

func _on_decision_timer_timeout():
	var current_phase = best_of_three_manager.get_current_phase()
	
	match current_phase:
		BestOfThreeManager.GamePhase.CARD_SELECTION:
			make_card_selection_decision()
		BestOfThreeManager.GamePhase.MODIFICATION:
			make_modification_decision()

func make_card_selection_decision():
	if has_made_decision:
		return
	
	print("AI: Making card selection decision...")
	
	# Analyze available cards
	var available_cards = hand_manager.player2_hand
	if available_cards.is_empty():
		print("AI Error: No cards available")
		return
	
	# Select best combination
	selected_cards = select_best_card_combination(available_cards)
	
	if not selected_cards.is_empty():
		# Submit selection
		best_of_three_manager.select_cards(2, selected_cards)
		has_made_decision = true
		print("AI: Selected ", selected_cards.size(), " cards")
		
		# Check if we can proceed to modification
		var p1_cards = best_of_three_manager.get_player_selected_cards(1)
		if not p1_cards.is_empty():
			best_of_three_manager.proceed_to_modification()
	else:
		print("AI Error: Could not select cards")

func select_best_card_combination(available_cards: Array) -> Array:
	var best_combination = []
	var best_score = -1
	
	# Generate all possible combinations (1-5 cards)
	for size in range(1, min(6, available_cards.size() + 1)):
		var combinations = generate_combinations(available_cards, size)
		
		for combo in combinations:
			var score = evaluate_combination(combo)
			if score > best_score:
				best_score = score
				best_combination = combo
	
	# Extract card IDs
	var card_ids = []
	for card in best_combination:
		card_ids.append(card.id)
	
	return card_ids

func generate_combinations(cards: Array, size: int) -> Array:
	if size == 0 or cards.is_empty():
		return [[]]
	
	if size > cards.size():
		return []
	
	var result = []
	var first_card = cards[0]
	var remaining_cards = cards.slice(1)
	
	# Combinations including the first card
	var with_first = generate_combinations(remaining_cards, size - 1)
	for combo in with_first:
		var new_combo = [first_card]
		new_combo.append_array(combo)
		result.append(new_combo)
	
	# Combinations not including the first card
	var without_first = generate_combinations(remaining_cards, size)
	result.append_array(without_first)
	
	return result

func evaluate_combination(cards: Array) -> float:
	if cards.is_empty():
		return 0.0
	
	# Convert to evaluation format
	var eval_cards = CardUtils.convert_cards_for_evaluation(cards)
	
	# Get poker hand value
	var poker_evaluator = load("res://Scripts/PokerHand.gd").new()
	var hand_value = poker_evaluator.evaluate_hand(eval_cards)
	var base_score = hand_manager.get_hand_score(hand_value)
	
	# Adjust score based on strategy
	var adjusted_score = float(base_score)
	
	# Prefer fewer cards for flexibility
	if cards.size() <= 3:
		adjusted_score += 0.5
	
	# Bonus for modification potential
	adjusted_score += evaluate_modification_potential(cards)
	
	return adjusted_score

func evaluate_modification_potential(cards: Array) -> float:
	var potential = 0.0
	
	# Look for cards that could benefit from modification
	for card in cards:
		var rank = card.get("value", 1)
		
		# Cards that could create pairs
		var rank_count = 0
		for other_card in cards:
			if other_card.get("value", 1) == rank:
				rank_count += 1
		
		if rank_count == 1:  # Single card that could become a pair
			potential += 0.3
		
		# Cards near straight potential
		if rank >= 2 and rank <= 12:
			potential += 0.2
	
	return potential

func handle_modification_phase():
	print("AI: Modification phase")
	
	# Analyze current situation
	var current_round = best_of_three_manager.get_current_round()
	var scores = best_of_three_manager.get_scores()
	var pressure_level = calculate_pressure_level(current_round, scores)
	
	# Decide on action
	var action = decide_modification_action(pressure_level)
	
	# Start timer for decision
	decision_timer.wait_time = randf_range(2.0, 4.0)
	decision_timer.start()

func make_modification_decision():
	var current_round = best_of_three_manager.get_current_round()
	var scores = best_of_three_manager.get_scores()
	var pressure_level = calculate_pressure_level(current_round, scores)
	
	var action = decide_modification_action(pressure_level)
	
	match action:
		"exchange":
			attempt_exchange()
		"modify_rank":
			attempt_rank_modification()
		"modify_suit":
			attempt_suit_modification()
		"validate":
			validate_immediately()

func calculate_pressure_level(round: int, scores: Dictionary) -> float:
	var ai_score = scores.player2
	var player_score = scores.player1
	var pressure = 0.0
	
	# Round pressure
	if round == 3:
		pressure += 0.4  # Final round
	elif round == 2:
		pressure += 0.2
	
	# Score pressure
	if player_score > ai_score:
		pressure += 0.3
	elif player_score == ai_score and round >= 2:
		pressure += 0.2
	
	# Must-win situation
	if round == 3 and player_score > ai_score:
		pressure += 0.5
	
	return clamp(pressure, 0.0, 1.0)

func decide_modification_action(pressure_level: float) -> String:
	# Adjust probabilities based on pressure
	var adjusted_cheat_prob = cheat_probability + (pressure_level * 0.3)
	var adjusted_exchange_prob = exchange_probability - (pressure_level * 0.1)
	
	var rand_value = randf()
	
	if rand_value < adjusted_exchange_prob:
		return "exchange"
	elif rand_value < adjusted_exchange_prob + adjusted_cheat_prob:
		# Choose between rank and suit modification
		return "modify_rank" if randf() < 0.7 else "modify_suit"
	else:
		return "validate"

func attempt_exchange():
	print("AI: Attempting exchange")
	
	# Find a card to exchange (worst card in selection)
	var selected_cards_info = hand_manager.get_cards_info(selected_cards)
	var worst_card = find_worst_card(selected_cards_info)
	
	if worst_card.is_empty():
		validate_immediately()
		return
	
	# Find a better card from remaining hand
	var remaining_cards = get_remaining_cards()
	var best_replacement = find_best_replacement(remaining_cards, selected_cards_info)
	
	if best_replacement.is_empty():
		validate_immediately()
		return
	
	# Perform exchange
	if best_of_three_manager.exchange_card(2, worst_card.id, best_replacement.id):
		print("AI: Exchanged card successfully")
		# Update selected cards
		var index = selected_cards.find(worst_card.id)
		if index >= 0:
			selected_cards[index] = best_replacement.id
	
	# Validate after exchange
	validate_immediately()

func attempt_rank_modification():
	print("AI: Attempting rank modification")
	
	if selected_cards.is_empty():
		validate_immediately()
		return
	
	# Choose a card to modify
	var card_id = selected_cards[randi() % selected_cards.size()]
	var card_info = hand_manager.get_card_info(card_id)
	
	if card_info.is_empty():
		validate_immediately()
		return
	
	# Choose new rank strategically
	var current_rank = card_info.get("value", 1)
	var new_rank = choose_strategic_rank(current_rank, selected_cards)
	var rank_change = new_rank - current_rank
	
	if best_of_three_manager.modify_card(2, card_id, "rank", rank_change):
		print("AI: Modified card rank successfully")
	
	validate_immediately()

func attempt_suit_modification():
	print("AI: Attempting suit modification")
	
	if selected_cards.is_empty():
		validate_immediately()
		return
	
	# Choose a card to modify
	var card_id = selected_cards[randi() % selected_cards.size()]
	var card_info = hand_manager.get_card_info(card_id)
	
	if card_info.is_empty():
		validate_immediately()
		return
	
	# Choose new suit strategically
	var new_suit = choose_strategic_suit(card_info, selected_cards)
	
	if best_of_three_manager.modify_card(2, card_id, "suit", new_suit):
		print("AI: Modified card suit successfully")
	
	validate_immediately()

func validate_immediately():
	print("AI: Validating hand")
	best_of_three_manager.validate_hand(2)

func find_worst_card(cards: Array) -> Dictionary:
	if cards.is_empty():
		return {}
	
	var worst_card = cards[0]
	for card in cards:
		if card.get("value", 1) < worst_card.get("value", 1):
			worst_card = card
	
	return worst_card

func get_remaining_cards() -> Array:
	var remaining = []
	var all_cards = hand_manager.player2_hand
	
	for card in all_cards:
		if not selected_cards.has(card.id):
			remaining.append(card)
	
	return remaining

func find_best_replacement(remaining_cards: Array, current_selection: Array) -> Dictionary:
	if remaining_cards.is_empty():
		return {}
	
	var best_card = remaining_cards[0]
	for card in remaining_cards:
		if card.get("value", 1) > best_card.get("value", 1):
			best_card = card
	
	return best_card

func choose_strategic_rank(current_rank: int, cards: Array) -> int:
	# Try to create pairs or improve to high cards
	var target_ranks = [1, 13, 12, 11, 10]  # Ace, King, Queen, Jack, 10
	
	for rank in target_ranks:
		if rank != current_rank:
			return rank
	
	return current_rank + 1

func choose_strategic_suit(card_info: Dictionary, cards: Array) -> String:
	var suits = ["hearts", "diamonds", "clubs", "spades"]
	var current_suit = card_info.get("suit", "hearts")
	
	# Try to create flush potential
	var suit_counts = {}
	var cards_info = hand_manager.get_cards_info(cards)
	
	for card in cards_info:
		var suit = card.get("suit", "hearts")
		suit_counts[suit] = suit_counts.get(suit, 0) + 1
	
	# Find most common suit (excluding current)
	var best_suit = current_suit
	var best_count = 0
	
	for suit in suit_counts.keys():
		if suit != current_suit and suit_counts[suit] > best_count:
			best_count = suit_counts[suit]
			best_suit = suit
	
	return best_suit
