extends Node
class_name EffectManager

const GameConfig = preload("res://Scripts/GameConfig.gd")

var active_effects := {}
var time_elapsed := 0.0
var initial_positions := {}

func _ready() -> void:
	pass

func _process(delta: float) -> void:
	time_elapsed += delta
	update_effects()

func update_effects() -> void:
	var cards_to_remove := []
	
	for card_id in active_effects.keys():
		var effect = active_effects[card_id]
		var card_button = get_card_button(card_id)

		# Check if card still exists
		if not card_button or not is_instance_valid(card_button):
			if effect.has("tween") and is_instance_valid(effect.tween):
				effect.tween.kill()  # Stop the tween
			cards_to_remove.append(card_id)
			continue
			
		if effect.has("tween") and not effect.tween.is_running():
			if effect.type in ["deselect", "hover"] or (effect.type == "select" and !card_button.get_meta("selected")):
				cards_to_remove.append(card_id)
	
	# Clean up effects from removed cards
	for card_id in cards_to_remove:
		active_effects.erase(card_id)


func store_initial_position(card_button: Control) -> void:
	var card_id = card_button.get_meta("card_id")
	if not initial_positions.has(card_id):
		initial_positions[card_id] = card_button.position.y

func get_initial_position(card_id: int) -> float:
	return initial_positions.get(card_id, 0.0)

func cleanup_existing_effect(card_id: int) -> void:
	if active_effects.has(card_id):
		var effect = active_effects[card_id]
		if effect.has("tween") and is_instance_valid(effect.tween):
			effect.tween.kill()
		active_effects.erase(card_id)

func play_card_selection_effect(card_id: int) -> void:
	var card_button = get_card_button(card_id)
	if not card_button or not is_instance_valid(card_button):
		return
		
	store_initial_position(card_button)
	cleanup_existing_effect(card_id)
	
	var tween = create_tween().bind_node(card_button)
	tween.set_parallel(true)
	
	var initial_y = get_initial_position(card_id)
	tween.tween_property(card_button, "position:y", 
		initial_y - GameConfig.SELECTION_LIFT, 0.3
	).set_trans(Tween.TRANS_CUBIC).set_ease(Tween.EASE_OUT)
	
	tween.tween_property(card_button, "modulate", 
		GameConfig.SELECTED_COLOR, 0.2
	)
	
	active_effects[card_id] = {
		"type": "select",
		"start_time": time_elapsed,
		"tween": tween
	}

func play_card_deselection_effect(card_id: int) -> void:
	var card_button = get_card_button(card_id)
	if not card_button or not is_instance_valid(card_button):
		return
		
	store_initial_position(card_button)
	cleanup_existing_effect(card_id)
	
	var tween = create_tween().bind_node(card_button)
	tween.set_parallel(true)
	
	var initial_y = get_initial_position(card_id)
	tween.tween_property(card_button, "position:y", 
		initial_y, 0.3
	).set_trans(Tween.TRANS_CUBIC).set_ease(Tween.EASE_OUT)
	
	tween.tween_property(card_button, "modulate", 
		GameConfig.NORMAL_COLOR, 0.2
	)
	
	active_effects[card_id] = {
		"type": "deselect",
		"start_time": time_elapsed,
		"tween": tween
	}

func play_hover_effect(card_id: int) -> void:
	var card_button = get_card_button(card_id)
	if not card_button or not is_instance_valid(card_button):
		return
		
	store_initial_position(card_button)
	cleanup_existing_effect(card_id)
	
	var tween = create_tween().bind_node(card_button)
	tween.set_parallel(true)
	
	var initial_y = get_initial_position(card_id)
	tween.tween_property(card_button, "position:y", 
		initial_y - GameConfig.HOVER_LIFT, 0.2
	).set_trans(Tween.TRANS_CUBIC).set_ease(Tween.EASE_OUT)
	
	tween.tween_property(card_button, "scale", 
		GameConfig.HOVER_SCALE, 0.2
	).set_trans(Tween.TRANS_CUBIC)
	
	active_effects[card_id] = {
		"type": "hover",
		"start_time": time_elapsed,
		"tween": tween
	}

func stop_hover_effect(card_id: int) -> void:
	var card_button = get_card_button(card_id)
	if not card_button or not is_instance_valid(card_button):
		return
		
	store_initial_position(card_button)
	cleanup_existing_effect(card_id)
	
	var tween = create_tween().bind_node(card_button)
	tween.set_parallel(true)
	
	var initial_y = get_initial_position(card_id)
	tween.tween_property(card_button, "position:y", 
		initial_y, 0.2
	).set_trans(Tween.TRANS_CUBIC).set_ease(Tween.EASE_OUT)
	
	tween.tween_property(card_button, "scale", 
		Vector2(1.0, 1.0), 0.2
	).set_trans(Tween.TRANS_CUBIC)
	
	active_effects.erase(card_id)

func get_card_button(card_id: int) -> TextureButton:
	var card_hand = get_node("../CardHand")
	if not card_hand:
		return null
	
	for child in card_hand.get_children():
		if child.has_meta("card_id") and child.get_meta("card_id") == card_id:
			return child
	return null
