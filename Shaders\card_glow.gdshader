shader_type canvas_item;

uniform vec4 glow_color : source_color = vec4(1.0, 0.8, 0.2, 1.0);
uniform float glow_intensity : hint_range(0.0, 1.0) = 0.5;
uniform float glow_scale : hint_range(1.0, 1.5) = 1.2;

void fragment() {
    vec4 texture_color = texture(TEXTURE, UV);
    vec2 centered_uv = (UV - 0.5) * 2.0;
    float distance_from_center = length(centered_uv);
    
    // Créer un effet de brillance qui s'estompe vers les bords
    float glow = 1.0 - smoothstep(0.0, 1.0, distance_from_center);
    glow = pow(glow, 2.0) * glow_intensity;
    
    // Ajouter la brillance à la couleur de base
    COLOR = texture_color + glow_color * glow;
}