extends Control
class_name NewCardGameController

# Core managers
var game_state_manager: GameStateManager
var hand_manager: HandManager
var strategic_ai: StrategicAI
var hand_reveal_system: HandRevealSystem

# UI Components
var player_ui: SimultaneousGameUI
var ai_ui: SimultaneousGameUI  # For visualization only

# Game state
var is_multiplayer: bool = false

func _ready():
	setup_managers()
	setup_ui()
	connect_signals()
	start_new_game()

func setup_managers():
	# Create core managers
	game_state_manager = GameStateManager.new()
	add_child(game_state_manager)
	
	hand_manager = HandManager.new()
	add_child(hand_manager)
	
	strategic_ai = StrategicAI.new(StrategicAI.Difficulty.MEDIUM)
	strategic_ai.set_managers(hand_manager, game_state_manager)
	add_child(strategic_ai)
	
	hand_reveal_system = HandRevealSystem.new()
	add_child(hand_reveal_system)

func setup_ui():
	# Create player UI
	player_ui = SimultaneousGameUI.new()
	player_ui.set_managers(hand_manager, game_state_manager)
	add_child(player_ui)
	
	# Position UI elements
	player_ui.position = Vector2(50, 50)
	hand_reveal_system.position = Vector2(200, 100)

func connect_signals():
	# Game state signals
	game_state_manager.game_state_changed.connect(_on_game_state_changed)
	game_state_manager.round_started.connect(_on_round_started)
	game_state_manager.round_ended.connect(_on_round_ended)
	game_state_manager.game_ended.connect(_on_game_ended)
	
	# UI signals
	player_ui.hand_submitted.connect(_on_player_hand_submitted)
	hand_reveal_system.reveal_completed.connect(_on_reveal_completed)

func start_new_game():
	print("Starting new Bluff Royale game!")
	game_state_manager.start_new_game()
	
	# Initialize hands for both players
	hand_manager.initialize_solo_game()
	
	# Start first round
	game_state_manager.start_round()

func _on_game_state_changed(new_state: String):
	print("Game state changed to: ", new_state)
	
	match new_state:
		"hand_preparation":
			handle_hand_preparation_phase()
		"hand_reveal":
			handle_hand_reveal_phase()
		"round_end":
			handle_round_end_phase()
		"game_end":
			handle_game_end_phase()

func _on_round_started(round_number: int):
	print("Round ", round_number, " started!")
	
	# Generate new hands for this round
	hand_manager.initialize_solo_game()
	
	# Reset cheat usage
	hand_manager.reset_cheat_usage()

func handle_hand_preparation_phase():
	print("Hand preparation phase - both players can now prepare their hands")
	
	# Player can interact with UI
	player_ui.enable_hand_preparation_ui()
	
	# AI makes its decision after a short delay
	var ai_timer = get_tree().create_timer(randf_range(2.0, 4.0))
	ai_timer.timeout.connect(_on_ai_decision_time)

func _on_ai_decision_time():
	print("AI is making its decision...")
	
	var ai_decision = strategic_ai.make_decision()
	print("AI decision: ", ai_decision)
	
	# Submit AI hand
	var ai_cards_info = hand_manager.get_cards_info(ai_decision.selected_cards)
	game_state_manager.submit_player_hand(
		2, 
		ai_cards_info, 
		ai_decision.cheated, 
		ai_decision.cheat_type
	)

func _on_player_hand_submitted(selected_cards: Array, cheated: bool, cheat_type: String):
	print("Player submitted hand: ", selected_cards.size(), " cards, cheated: ", cheated)
	
	# Submit player hand to game state manager
	game_state_manager.submit_player_hand(1, selected_cards, cheated, cheat_type)

func handle_hand_reveal_phase():
	print("Hand reveal phase - showing both hands")
	
	# Get the round results
	var round_results = get_current_round_results()
	
	# Show the reveal animation
	hand_reveal_system.reveal_hands(round_results)

func get_current_round_results() -> Dictionary:
	# Get hand data from game state manager
	var p1_hand_data = game_state_manager.player1_hand_data
	var p2_hand_data = game_state_manager.player2_hand_data
	
	# Get the actual results that were calculated
	var results = {
		"player1_cards": p1_hand_data.get("cards", []),
		"player2_cards": p2_hand_data.get("cards", []),
		"player1_hand": "",
		"player2_hand": "",
		"player1_cheated": game_state_manager.player1_cheated,
		"player2_cheated": game_state_manager.player2_cheated,
		"player1_cheat_type": game_state_manager.player1_cheat_type,
		"player2_cheat_type": game_state_manager.player2_cheat_type,
		"winner": 0,
		"reason": ""
	}
	
	# Evaluate hands for display
	var poker_evaluator = load("res://Scripts/PokerHand.gd").new()
	
	if not results.player1_cards.is_empty():
		var p1_eval = CardUtils.convert_cards_for_evaluation(results.player1_cards)
		results.player1_hand = poker_evaluator.evaluate_hand(p1_eval)
	
	if not results.player2_cards.is_empty():
		var p2_eval = CardUtils.convert_cards_for_evaluation(results.player2_cards)
		results.player2_hand = poker_evaluator.evaluate_hand(p2_eval)
	
	return results

func _on_round_ended(winner: int, results: Dictionary):
	print("Round ended - Winner: ", winner)
	print("Results: ", results)

func _on_reveal_completed(results: Dictionary):
	print("Reveal completed, moving to next phase")
	
	# Check if game is over
	var scores = game_state_manager.get_scores()
	var current_round = game_state_manager.get_current_round()
	
	if current_round >= 3 or scores.player1 > 1 or scores.player2 > 1:
		# Game is over
		handle_game_end_phase()
	else:
		# Continue to next round
		show_round_end_screen(results)

func show_round_end_screen(results: Dictionary):
	# Create a simple continue button
	var continue_button = Button.new()
	continue_button.text = "Next Round"
	continue_button.position = Vector2(400, 400)
	continue_button.size = Vector2(200, 50)
	continue_button.pressed.connect(_on_next_round_pressed)
	add_child(continue_button)

func _on_next_round_pressed():
	# Remove the continue button
	for child in get_children():
		if child is Button and child.text == "Next Round":
			child.queue_free()
	
	# Start next round
	game_state_manager.next_round()

func handle_round_end_phase():
	print("Round end phase")

func handle_game_end_phase():
	print("Game end phase")
	
	var scores = game_state_manager.get_scores()
	var final_winner = 1 if scores.player1 > scores.player2 else (2 if scores.player2 > scores.player1 else 0)
	
	show_game_end_screen(final_winner, scores)

func show_game_end_screen(winner: int, scores: Dictionary):
	# Create game end UI
	var end_screen = Panel.new()
	end_screen.size = Vector2(400, 300)
	end_screen.position = Vector2(200, 150)
	add_child(end_screen)
	
	var end_label = Label.new()
	if winner == 1:
		end_label.text = "Player 1 Wins!\nFinal Score: " + str(scores.player1) + " - " + str(scores.player2)
	elif winner == 2:
		end_label.text = "Player 2 Wins!\nFinal Score: " + str(scores.player1) + " - " + str(scores.player2)
	else:
		end_label.text = "It's a Tie!\nFinal Score: " + str(scores.player1) + " - " + str(scores.player2)
	
	end_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	end_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	end_label.size = Vector2(400, 200)
	end_screen.add_child(end_label)
	
	# Restart button
	var restart_button = Button.new()
	restart_button.text = "Play Again"
	restart_button.position = Vector2(150, 220)
	restart_button.size = Vector2(100, 40)
	restart_button.pressed.connect(_on_restart_pressed)
	end_screen.add_child(restart_button)

func _on_restart_pressed():
	get_tree().reload_current_scene()

func _on_game_ended(final_winner: int, final_scores: Dictionary):
	print("Game ended - Final winner: ", final_winner)
	print("Final scores: ", final_scores)

# Debug functions
func _input(event):
	if event.is_action_pressed("ui_accept"):  # Enter key
		print("=== DEBUG INFO ===")
		print("Game State: ", game_state_manager.get_current_state())
		print("Round: ", game_state_manager.get_current_round())
		print("Scores: ", game_state_manager.get_scores())
		print("Players Ready: P1=", game_state_manager.is_player_ready(1), " P2=", game_state_manager.is_player_ready(2))
