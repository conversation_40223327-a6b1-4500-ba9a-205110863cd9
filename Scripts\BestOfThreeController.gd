extends Control
class_name BestOfThreeController

# Core managers
var best_of_three_manager: BestOfThreeManager
var hand_manager: HandManager
var ai_player: BestOfThreeAI
var ui_manager: BestOfThreeUI
var reveal_system: HandRevealSystem

# Game state
var is_multiplayer: bool = false

func _ready():
	setup_managers()
	setup_ui()
	connect_signals()
	start_new_game()

func setup_managers():
	# Create core managers
	best_of_three_manager = BestOfThreeManager.new()
	add_child(best_of_three_manager)
	
	hand_manager = HandManager.new()
	add_child(hand_manager)
	
	ai_player = BestOfThreeAI.new(BestOfThreeAI.Difficulty.MEDIUM)
	add_child(ai_player)
	
	reveal_system = HandRevealSystem.new()
	add_child(reveal_system)
	
	# Set manager references
	best_of_three_manager.set_hand_manager(hand_manager)
	ai_player.set_managers(best_of_three_manager, hand_manager)

func setup_ui():
	# Create UI manager
	ui_manager = BestOfThreeUI.new()
	ui_manager.set_managers(best_of_three_manager, hand_manager)
	add_child(ui_manager)
	
	# Position UI elements
	ui_manager.position = Vector2(50, 50)
	reveal_system.position = Vector2(200, 100)

func connect_signals():
	# Game manager signals
	best_of_three_manager.round_started.connect(_on_round_started)
	best_of_three_manager.phase_changed.connect(_on_phase_changed)
	best_of_three_manager.round_ended.connect(_on_round_ended)
	best_of_three_manager.game_ended.connect(_on_game_ended)
	
	# UI signals
	ui_manager.cards_selected.connect(_on_cards_selected)
	ui_manager.card_exchanged.connect(_on_card_exchanged)
	ui_manager.card_modified.connect(_on_card_modified)
	ui_manager.hand_validated.connect(_on_hand_validated)
	
	# Reveal system signals
	reveal_system.reveal_completed.connect(_on_reveal_completed)

func start_new_game():
	print("=== STARTING BEST OF THREE GAME ===")
	best_of_three_manager.start_new_game()

func _on_round_started(round_number: int):
	print("Round ", round_number, " started!")
	
	# Reset any round-specific state
	if hand_manager:
		hand_manager.clear_selection()

func _on_phase_changed(new_phase: String):
	print("Phase changed to: ", new_phase)
	
	match new_phase:
		"card_selection":
			handle_card_selection_phase()
		"modification":
			handle_modification_phase()
		"validation":
			handle_validation_phase()
		"reveal":
			handle_reveal_phase()
		"round_end":
			handle_round_end_phase()

func handle_card_selection_phase():
	print("Card Selection Phase - Choose your cards")
	# UI is automatically updated by BestOfThreeUI

func handle_modification_phase():
	print("Modification Phase - Exchange or modify cards")
	# UI is automatically updated by BestOfThreeUI

func handle_validation_phase():
	print("Validation Phase - Waiting for players")
	# UI is automatically updated by BestOfThreeUI

func handle_reveal_phase():
	print("Reveal Phase - Showing hands")
	
	# Get round results from the manager
	# The results are calculated in BestOfThreeManager.determine_round_winner()
	# We'll get them in the round_ended signal

func handle_round_end_phase():
	print("Round End Phase")

func _on_round_ended(winner: int, results: Dictionary):
	print("Round ended - Winner: ", winner)
	print("Results: ", results)
	
	# Show the reveal animation
	show_hand_reveal(results)

func show_hand_reveal(results: Dictionary):
	# Prepare data for reveal system
	var reveal_data = {
		"player1_cards": results.get("player1_cards", []),
		"player2_cards": results.get("player2_cards", []),
		"player1_hand": results.get("player1_hand", ""),
		"player2_hand": results.get("player2_hand", ""),
		"player1_cheated": results.get("player1_cheated", false),
		"player2_cheated": results.get("player2_cheated", false),
		"player1_cheat_type": results.get("player1_modification", ""),
		"player2_cheat_type": results.get("player2_modification", ""),
		"winner": results.get("winner", 0),
		"reason": results.get("reason", "")
	}
	
	reveal_system.reveal_hands(reveal_data)

func _on_reveal_completed(results: Dictionary):
	print("Reveal completed")
	
	# Check if game is over
	var scores = best_of_three_manager.get_scores()
	var current_round = best_of_three_manager.get_current_round()
	
	if scores.player1 >= 2 or scores.player2 >= 2 or current_round >= 3:
		# Game is over, handled by game_ended signal
		return
	else:
		# Show continue to next round button
		show_next_round_button()

func show_next_round_button():
	# Create a simple continue button
	var continue_button = Button.new()
	continue_button.text = "Next Round"
	continue_button.position = Vector2(400, 400)
	continue_button.size = Vector2(200, 50)
	continue_button.pressed.connect(_on_next_round_pressed)
	add_child(continue_button)

func _on_next_round_pressed():
	# Remove the continue button
	for child in get_children():
		if child is Button and child.text == "Next Round":
			child.queue_free()
	
	# Start next round
	best_of_three_manager.next_round()

func _on_game_ended(final_winner: int, scores: Dictionary):
	print("Game ended - Final winner: ", final_winner)
	print("Final scores: ", scores)
	
	show_game_end_screen(final_winner, scores)

func show_game_end_screen(winner: int, scores: Dictionary):
	# Create game end UI
	var end_screen = Panel.new()
	end_screen.size = Vector2(400, 300)
	end_screen.position = Vector2(200, 150)
	add_child(end_screen)
	
	var end_label = Label.new()
	if winner == 1:
		end_label.text = "YOU WIN!\nFinal Score: " + str(scores.player1) + " - " + str(scores.player2)
	elif winner == 2:
		end_label.text = "AI WINS!\nFinal Score: " + str(scores.player1) + " - " + str(scores.player2)
	else:
		end_label.text = "IT'S A TIE!\nFinal Score: " + str(scores.player1) + " - " + str(scores.player2)
	
	end_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	end_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	end_label.size = Vector2(400, 200)
	end_screen.add_child(end_label)
	
	# Restart button
	var restart_button = Button.new()
	restart_button.text = "Play Again"
	restart_button.position = Vector2(150, 220)
	restart_button.size = Vector2(100, 40)
	restart_button.pressed.connect(_on_restart_pressed)
	end_screen.add_child(restart_button)

func _on_restart_pressed():
	get_tree().reload_current_scene()

# UI Signal handlers
func _on_cards_selected(card_ids: Array):
	print("Player selected cards: ", card_ids)
	# This is handled by the UI manager directly

func _on_card_exchanged(old_card_id: int, new_card_id: int):
	print("Player exchanged card: ", old_card_id, " -> ", new_card_id)
	# This is handled by the UI manager directly

func _on_card_modified(card_id: int, modification_type: String, new_value):
	print("Player modified card: ", card_id, " (", modification_type, ")")
	# This is handled by the UI manager directly

func _on_hand_validated():
	print("Player validated hand")
	# This is handled by the UI manager directly

# Debug functions
func _input(event):
	if event.is_action_pressed("ui_accept"):  # Enter key
		print_debug_info()

func print_debug_info():
	print("=== DEBUG INFO ===")
	if best_of_three_manager:
		print("Current Phase: ", best_of_three_manager.get_current_phase())
		print("Current Round: ", best_of_three_manager.get_current_round())
		print("Scores: ", best_of_three_manager.get_scores())
		print("Player 1 validated: ", best_of_three_manager.is_player_validated(1))
		print("Player 2 validated: ", best_of_three_manager.is_player_validated(2))
		print("Player 1 selected: ", best_of_three_manager.get_player_selected_cards(1))
		print("Player 2 selected: ", best_of_three_manager.get_player_selected_cards(2))
	
	if hand_manager:
		print("Player 1 hand size: ", hand_manager.player1_hand.size())
		print("Player 2 hand size: ", hand_manager.player2_hand.size())
	
	print("===================")
