extends Control

const PORT = 8910
var peer = ENetMultiplayerPeer.new()
var upnp = UPNP.new()

# Liste des salles
var public_rooms = {}
var current_room_id = ""
var is_joining = false

# Ajouter un timer pour le rafraîchissement automatique
var refresh_timer: Timer

# UI nodes
@onready var waiting_screen = $WaitingScreen
@onready var lobby_container = $VBoxContainer
@onready var create_button = $VBoxContainer/CreateRoom/CreateButton
@onready var join_private_button = $VBoxContainer/JoinPrivate/JoinPrivateButton
@onready var refresh_button = $VBoxContainer/PublicRooms/RefreshButton
@onready var join_public_button = $VBoxContainer/PublicRooms/JoinPublicButton
@onready var back_button = $BackButton
@onready var cancel_button = $WaitingScreen/CancelButton
@onready var copy_code_button = $WaitingScreen/CopyCodeButton

func _ready():
	# Vérifier que tous les nodes UI existent
	assert(waiting_screen != null, "WaitingScreen node not found")
	assert(lobby_container != null, "VBoxContainer node not found")
	assert(create_button != null, "CreateButton node not found")
	assert(join_private_button != null, "JoinPrivateButton node not found")
	assert(refresh_button != null, "RefreshButton node not found")
	assert(join_public_button != null, "JoinPublicButton node not found")
	assert(back_button != null, "BackButton node not found")
	assert(cancel_button != null, "CancelButton node not found")
	assert(copy_code_button != null, "CopyCodeButton node not found")
	
	# Connecter les signaux des boutons
	create_button.pressed.connect(_on_create_room)
	join_private_button.pressed.connect(_on_join_private_room)
	refresh_button.pressed.connect(_on_refresh_rooms)
	join_public_button.pressed.connect(_on_join_public_room)
	back_button.pressed.connect(_on_back_pressed)
	cancel_button.pressed.connect(_on_cancel_waiting)
	copy_code_button.pressed.connect(_on_copy_code_pressed)
	
	# Connecter les signaux réseau
	multiplayer.peer_connected.connect(_on_peer_connected)
	multiplayer.peer_disconnected.connect(_on_peer_disconnected)
	
	# Cacher l'écran d'attente au démarrage
	waiting_screen.hide()
	lobby_container.show()
	
	# Rafraîchir la liste des salles au démarrage
	refresh_room_list()

	# Créer et configurer le timer de rafraîchissement
	refresh_timer = Timer.new()
	refresh_timer.wait_time = 2.0  # Rafraîchir toutes les 2 secondes
	refresh_timer.timeout.connect(_on_refresh_timer_timeout)
	add_child(refresh_timer)
	refresh_timer.start()

func _on_create_room():
	var room_name = $VBoxContainer/CreateRoom/RoomName.text
	var is_private = $VBoxContainer/CreateRoom/IsPrivate.button_pressed
	
	if room_name.is_empty():
		show_error("Veuillez entrer un nom de salle")
		return
	
	# Créer le serveur
	var error = peer.create_server(PORT)
	if error != OK:
		show_error("Impossible de créer le serveur")
		return
	
	# Configurer le multiplayer
	multiplayer.multiplayer_peer = peer
	
	# Générer et enregistrer la salle
	current_room_id = generate_room_code()
	
	# Créer un dictionnaire avec les informations de la salle
	var room_info = {
		"name": room_name,
		"players": 1,
		"max_players": 2,
		"host": multiplayer.get_unique_id()
	}
	
	if not is_private:
		# Ajouter la salle à la liste publique
		public_rooms[current_room_id] = room_info
		# Rafraîchir immédiatement l'affichage
		refresh_room_list()
	
	# Afficher l'écran d'attente
	show_waiting_screen(room_name, current_room_id)

func show_waiting_screen(room_name: String, code: String):
	lobby_container.hide()
	waiting_screen.show()
	waiting_screen.get_node("RoomInfo").text = "Salle : %s\nCode : %s" % [room_name, code]
	if multiplayer.is_server():
		waiting_screen.get_node("WaitingLabel").text = "En attente d'un autre joueur..."
	else:
		waiting_screen.get_node("WaitingLabel").text = "Connexion à la salle..."

func _on_cancel_waiting():
	is_joining = false
	if multiplayer.multiplayer_peer:
		multiplayer.multiplayer_peer.close()
	
	if current_room_id in public_rooms:
		public_rooms.erase(current_room_id)
		refresh_room_list()
	
	waiting_screen.hide()
	lobby_container.show()
	current_room_id = ""

func _on_join_private_room():
	var room_code = $VBoxContainer/JoinPrivate/RoomCode.text
	
	if room_code.is_empty():
		show_error("Veuillez entrer un code de salle")
		return
	
	join_room(room_code)

func _on_join_public_room():
	var selected_items = $VBoxContainer/PublicRooms/RoomList.get_selected_items()
	if selected_items.is_empty():
		show_error("Veuillez sélectionner une salle")
		return
	
	var room_id = $VBoxContainer/PublicRooms/RoomList.get_item_metadata(selected_items[0])
	join_room(room_id)

func join_room(room_id: String):
	is_joining = true
	var error = peer.create_client("127.0.0.1", PORT)
	if error != OK:
		show_error("Impossible de rejoindre la salle")
		return
	
	# Configurer le multiplayer
	multiplayer.multiplayer_peer = peer
	
	# Attendre un moment pour que la connexion s'établisse
	await get_tree().create_timer(0.1).timeout
	
	current_room_id = room_id
	# Afficher l'écran d'attente pour le joueur qui rejoint
	show_waiting_screen("Salle rejointe", room_id)

func _on_refresh_rooms():
	refresh_room_list()

func refresh_room_list():
	var room_list = $VBoxContainer/PublicRooms/RoomList
	room_list.clear()
	
	for room_id in public_rooms:
		var room = public_rooms[room_id]
		var text = "%s (%d/%d)" % [room.name, room.players, room.max_players]
		room_list.add_item(text)
		room_list.set_item_metadata(room_list.get_item_count() - 1, room_id)

func _on_back_pressed():
	if multiplayer.multiplayer_peer:
		multiplayer.multiplayer_peer.close()
	get_tree().change_scene_to_file("res://Scenes/MainMenu.tscn")

func _on_peer_connected(_id: int):
	if multiplayer.is_server():
		# Mettre à jour le nombre de joueurs dans la salle
		if current_room_id in public_rooms:
			public_rooms[current_room_id].players += 1
			refresh_room_list()
		# Démarrer la partie maintenant que le second joueur est connecté
		start_game()
	elif is_joining:
		# Le client est connecté au serveur, démarrer la partie
		start_game()

func _on_peer_disconnected(_id: int):
	if multiplayer.is_server():
		if current_room_id in public_rooms:
			public_rooms[current_room_id].players -= 1
			if public_rooms[current_room_id].players <= 0:
				public_rooms.erase(current_room_id)
			refresh_room_list()

func start_game():
	# Assurez-vous que la scène existe
	var scene_path = "res://Scenes/CardGame.tscn"
	if ResourceLoader.exists(scene_path):
		get_tree().change_scene_to_file(scene_path)
	else:
		show_error("Impossible de charger la scène de jeu")

func setup_upnp():
	var discover_result = upnp.discover()
	if discover_result == UPNP.UPNP_RESULT_SUCCESS:
		if upnp.get_gateway() and upnp.get_gateway().is_valid_gateway():
			upnp.add_port_mapping(PORT)

func generate_room_code() -> String:
	var chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	var code = ""
	for i in range(6):
		code += chars[randi() % chars.length()]
	return code

func show_error(message: String):
	var dialog = AcceptDialog.new()
	dialog.dialog_text = message
	add_child(dialog)
	dialog.popup_centered()

func _on_refresh_timer_timeout():
	refresh_room_list()

# Ajouter cette fonction pour nettoyer les ressources
func _exit_tree():
	if refresh_timer:
		refresh_timer.stop()
		refresh_timer.queue_free()
	
	if current_room_id in public_rooms:
		public_rooms.erase(current_room_id)
	
	if multiplayer.multiplayer_peer:
		multiplayer.multiplayer_peer.close()

func _on_copy_code_pressed():
	if current_room_id != "":
		DisplayServer.clipboard_set(current_room_id)
		show_message("Code copié !")

func show_message(message: String):
	var dialog = AcceptDialog.new()
	dialog.dialog_text = message
	add_child(dialog)
	dialog.popup_centered()
	# Supprimer automatiquement la boîte de dialogue après quelques secondes
	await get_tree().create_timer(2.0).timeout
	dialog.queue_free()

