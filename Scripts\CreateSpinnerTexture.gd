@tool
extends EditorScript

func _run():
	var image = Image.create(64, 64, false, Image.FORMAT_RGBA8)
	image.fill(Color(0, 0, 0, 0))
	
	# Dessiner un cercle avec des points
	var center = Vector2(32, 32)
	var radius = 24
	var num_points = 8
	
	for i in range(num_points):
		var angle = i * 2 * PI / num_points
		var pos = center + Vector2(cos(angle), sin(angle)) * radius
		var color = Color.WHITE
		color.a = float(i + 1) / num_points
		
		# Dessiner un point
		for x in range(-2, 3):
			for y in range(-2, 3):
				if x * x + y * y <= 4:
					var px = pos.x + x
					var py = pos.y + y
					if px >= 0 and px < 64 and py >= 0 and py < 64:
						image.set_pixel(px, py, color)
	
	var texture = ImageTexture.create_from_image(image)
	
	# Sauvegarder la texture
	var err = ResourceSaver.save(texture, "res://Assets/spinner.png")
	if err != OK:
		push_error("Failed to save spinner texture")
	else:
		print("Spinner texture saved successfully")