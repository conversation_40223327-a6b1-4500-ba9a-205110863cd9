extends Control
class_name VisualHandManager

signal card_selected(card_id: int)
signal card_deselected(card_id: int)
signal selection_changed(selected_cards: Array)

# Hand data
var hand_cards: Array = []
var visual_cards: Array = []
var selected_card_ids: Array = []
var max_selection: int = 5

# Layout
var card_spacing: float = 10.0
var hand_container: HBoxContainer

func _ready():
	setup_hand_container()

func setup_hand_container():
	hand_container = HBoxContainer.new()
	hand_container.alignment = BoxContainer.ALIGNMENT_CENTER
	hand_container.add_theme_constant_override("separation", card_spacing)
	add_child(hand_container)

func set_hand(cards: Array):
	clear_hand()
	hand_cards = cards.duplicate()
	create_visual_cards()

func clear_hand():
	# Clean up existing visual cards
	for visual_card in visual_cards:
		if visual_card and is_instance_valid(visual_card):
			visual_card.cleanup()
	
	visual_cards.clear()
	selected_card_ids.clear()
	
	# Clear container
	if hand_container:
		for child in hand_container.get_children():
			child.queue_free()

func create_visual_cards():
	for card_data in hand_cards:
		var visual_card = VisualCard.new(card_data)
		visual_card.card_clicked.connect(_on_card_clicked)
		visual_card.card_hovered.connect(_on_card_hovered)
		visual_card.card_unhovered.connect(_on_card_unhovered)
		
		visual_cards.append(visual_card)
		hand_container.add_child(visual_card)
		
		# Set initial position for animations
		await get_tree().process_frame
		visual_card.original_position = visual_card.position

func _on_card_clicked(card_id: int):
	toggle_card_selection(card_id)

func _on_card_hovered(card_id: int):
	# Optional: Add hover effects or tooltips
	pass

func _on_card_unhovered(card_id: int):
	# Optional: Remove hover effects
	pass

func toggle_card_selection(card_id: int):
	if selected_card_ids.has(card_id):
		# Deselect card
		deselect_card(card_id)
	else:
		# Select card (if under limit)
		if selected_card_ids.size() < max_selection:
			select_card(card_id)
		else:
			show_selection_limit_message()

func select_card(card_id: int):
	if not selected_card_ids.has(card_id):
		selected_card_ids.append(card_id)
		
		# Update visual card
		var visual_card = get_visual_card_by_id(card_id)
		if visual_card:
			visual_card.set_selected(true)
		
		emit_signal("card_selected", card_id)
		emit_signal("selection_changed", selected_card_ids.duplicate())

func deselect_card(card_id: int):
	if selected_card_ids.has(card_id):
		selected_card_ids.erase(card_id)
		
		# Update visual card
		var visual_card = get_visual_card_by_id(card_id)
		if visual_card:
			visual_card.set_selected(false)
		
		emit_signal("card_deselected", card_id)
		emit_signal("selection_changed", selected_card_ids.duplicate())

func clear_selection():
	var cards_to_deselect = selected_card_ids.duplicate()
	for card_id in cards_to_deselect:
		deselect_card(card_id)

func get_visual_card_by_id(card_id: int) -> VisualCard:
	for visual_card in visual_cards:
		if visual_card and visual_card.card_id == card_id:
			return visual_card
	return null

func get_selected_cards() -> Array:
	return selected_card_ids.duplicate()

func get_selected_card_data() -> Array:
	var selected_data = []
	for card_id in selected_card_ids:
		for card_data in hand_cards:
			if card_data.get("id") == card_id:
				selected_data.append(card_data)
				break
	return selected_data

func update_card(card_id: int, new_data: Dictionary):
	# Update hand data
	for i in range(hand_cards.size()):
		if hand_cards[i].get("id") == card_id:
			hand_cards[i] = new_data
			break
	
	# Update visual card
	var visual_card = get_visual_card_by_id(card_id)
	if visual_card:
		visual_card.update_card_data(new_data)

func set_max_selection(max_cards: int):
	max_selection = max_cards

func is_card_selected(card_id: int) -> bool:
	return selected_card_ids.has(card_id)

func get_selection_count() -> int:
	return selected_card_ids.size()

func show_selection_limit_message():
	# This could be connected to a UI message system
	print("Maximum ", max_selection, " cards can be selected")

func animate_hand_deal():
	# Animate cards being dealt
	for i in range(visual_cards.size()):
		var visual_card = visual_cards[i]
		if visual_card:
			# Start cards off-screen
			visual_card.position.x = -200
			visual_card.modulate.a = 0
			
			# Animate them sliding in
			var tween = create_tween()
			tween.set_parallel(true)
			
			# Delay each card slightly
			await get_tree().create_timer(i * 0.1).timeout
			
			tween.tween_property(visual_card, "position:x", visual_card.original_position.x, 0.5)
			tween.tween_property(visual_card, "modulate:a", 1.0, 0.5)

func animate_card_exchange(old_card_id: int, new_card_data: Dictionary):
	var visual_card = get_visual_card_by_id(old_card_id)
	if not visual_card:
		return
	
	# Animate card flipping
	var tween = create_tween()
	tween.set_parallel(true)
	
	# Flip out
	tween.tween_property(visual_card, "scale:x", 0.0, 0.2)
	
	# Wait for flip, then update data and flip back in
	await tween.finished
	visual_card.update_card_data(new_card_data)
	
	var flip_in_tween = create_tween()
	flip_in_tween.tween_property(visual_card, "scale:x", 1.0, 0.2)

func highlight_cards(card_ids: Array, color: Color = Color.YELLOW):
	for card_id in card_ids:
		var visual_card = get_visual_card_by_id(card_id)
		if visual_card:
			var tween = create_tween()
			tween.tween_property(visual_card, "modulate", color, 0.3)

func remove_highlights():
	for visual_card in visual_cards:
		if visual_card:
			var tween = create_tween()
			var target_color = GameConfig.SELECTED_COLOR if visual_card.is_selected else Color.WHITE
			tween.tween_property(visual_card, "modulate", target_color, 0.3)

func disable_interaction():
	for visual_card in visual_cards:
		if visual_card and visual_card.card_button:
			visual_card.card_button.disabled = true

func enable_interaction():
	for visual_card in visual_cards:
		if visual_card and visual_card.card_button:
			visual_card.card_button.disabled = false
