extends Node
class_name StrategicAI

# AI difficulty levels
enum Difficulty {
	EASY,    # 30% cheat chance, basic hand selection
	MEDIUM,  # 50% cheat chance, good hand selection
	HARD     # 70% cheat chance, optimal hand selection
}

var difficulty: Difficulty = Difficulty.MEDIUM
var hand_manager: HandManager
var game_state_manager: GameStateManager

# AI decision parameters
var cheat_probability: float = 0.5
var risk_tolerance: float = 0.6
var bluff_tendency: float = 0.3

func _init(diff: Difficulty = Difficulty.MEDIUM):
	difficulty = diff
	set_difficulty_parameters()

func set_difficulty_parameters():
	match difficulty:
		Difficulty.EASY:
			cheat_probability = 0.3
			risk_tolerance = 0.4
			bluff_tendency = 0.2
		Difficulty.MEDIUM:
			cheat_probability = 0.5
			risk_tolerance = 0.6
			bluff_tendency = 0.3
		Difficulty.HARD:
			cheat_probability = 0.7
			risk_tolerance = 0.8
			bluff_tendency = 0.4

func set_managers(hand_mgr: HandManager, state_mgr: GameStateManager):
	hand_manager = hand_mgr
	game_state_manager = state_mgr

# Main AI decision making function
func make_decision() -> Dictionary:
	if not hand_manager or not game_state_manager:
		push_error("AI managers not set")
		return {}
	
	var current_round = game_state_manager.get_current_round()
	var scores = game_state_manager.get_scores()
	var ai_score = scores.player2
	var player_score = scores.player1
	
	# Analyze current situation
	var situation = analyze_game_situation(current_round, ai_score, player_score)
	
	# Decide whether to cheat
	var should_cheat = decide_to_cheat(situation)
	
	# Select best hand
	var selected_cards = select_optimal_hand(should_cheat)
	
	# Apply cheat if decided
	var cheat_applied = false
	var cheat_type = ""
	
	if should_cheat and selected_cards.size() > 0:
		var cheat_result = apply_strategic_cheat(selected_cards)
		cheat_applied = cheat_result.success
		cheat_type = cheat_result.type
	
	return {
		"selected_cards": selected_cards,
		"cheated": cheat_applied,
		"cheat_type": cheat_type,
		"reasoning": get_decision_reasoning(situation, should_cheat, cheat_applied)
	}

func analyze_game_situation(round: int, ai_score: int, player_score: int) -> Dictionary:
	var situation = {
		"round": round,
		"ai_score": ai_score,
		"player_score": player_score,
		"is_final_round": round >= 3,
		"ai_winning": ai_score > player_score,
		"score_difference": ai_score - player_score,
		"pressure_level": 0.0
	}
	
	# Calculate pressure level (0.0 = no pressure, 1.0 = maximum pressure)
	if situation.is_final_round:
		if ai_score < player_score:
			situation.pressure_level = 1.0  # Must win
		elif ai_score == player_score:
			situation.pressure_level = 0.8  # High pressure
		else:
			situation.pressure_level = 0.2  # Low pressure, already winning
	else:
		# Early rounds
		if player_score > ai_score:
			situation.pressure_level = 0.6
		else:
			situation.pressure_level = 0.3
	
	return situation

func decide_to_cheat(situation: Dictionary) -> bool:
	var base_cheat_chance = cheat_probability
	
	# Adjust cheat probability based on situation
	var adjusted_chance = base_cheat_chance
	
	# Increase cheat chance under pressure
	adjusted_chance += situation.pressure_level * 0.3
	
	# Increase cheat chance if losing
	if not situation.ai_winning:
		adjusted_chance += 0.2
	
	# Final round desperation
	if situation.is_final_round and situation.ai_score < situation.player_score:
		adjusted_chance += 0.4
	
	# Random factor
	adjusted_chance += (randf() - 0.5) * 0.2
	
	# Clamp between 0 and 1
	adjusted_chance = clamp(adjusted_chance, 0.0, 1.0)
	
	return randf() < adjusted_chance

func select_optimal_hand(will_cheat: bool) -> Array:
	if not hand_manager:
		return []
	
	var available_cards = hand_manager.player2_hand
	if available_cards.is_empty():
		return []
	
	# Get all possible combinations
	var all_combinations = get_all_hand_combinations(available_cards)
	
	var best_combination = []
	var best_score = -1
	
	# Evaluate each combination
	for combination in all_combinations:
		var score = evaluate_hand_combination(combination, will_cheat)
		if score > best_score:
			best_score = score
			best_combination = combination
	
	# Extract card IDs
	var selected_ids = []
	for card in best_combination:
		selected_ids.append(card.id)
	
	return selected_ids

func get_all_hand_combinations(cards: Array) -> Array:
	var combinations = []
	
	# Generate combinations of 1 to 5 cards
	for size in range(1, min(6, cards.size() + 1)):
		var combos = generate_combinations(cards, size)
		combinations.append_array(combos)
	
	return combinations

func generate_combinations(cards: Array, size: int) -> Array:
	if size == 0 or cards.is_empty():
		return [[]]
	
	if size > cards.size():
		return []
	
	var result = []
	var first_card = cards[0]
	var remaining_cards = cards.slice(1)
	
	# Combinations including the first card
	var with_first = generate_combinations(remaining_cards, size - 1)
	for combo in with_first:
		var new_combo = [first_card]
		new_combo.append_array(combo)
		result.append(new_combo)
	
	# Combinations not including the first card
	var without_first = generate_combinations(remaining_cards, size)
	result.append_array(without_first)
	
	return result

func evaluate_hand_combination(cards: Array, will_cheat: bool) -> float:
	if cards.is_empty():
		return 0.0
	
	# Convert to evaluation format
	var eval_cards = CardUtils.convert_cards_for_evaluation(cards)
	
	# Get poker hand value
	var poker_evaluator = load("res://Scripts/PokerHand.gd").new()
	var hand_value = poker_evaluator.evaluate_hand(eval_cards)
	var base_score = hand_manager.get_hand_score(hand_value)
	
	# Adjust score based on strategy
	var adjusted_score = float(base_score)
	
	# Bonus for fewer cards (more risky but potentially higher value)
	if cards.size() <= 3:
		adjusted_score += risk_tolerance * 0.5
	
	# Penalty for too many cards
	if cards.size() > 4:
		adjusted_score -= 0.3
	
	# If planning to cheat, consider cheat potential
	if will_cheat:
		adjusted_score += evaluate_cheat_potential(cards)
	
	return adjusted_score

func evaluate_cheat_potential(cards: Array) -> float:
	if cards.is_empty():
		return 0.0
	
	var potential = 0.0
	
	# Look for cards that could benefit from rank changes
	for card in cards:
		var rank = card.get("value", 1)
		
		# Cards near straight potential
		if rank >= 2 and rank <= 12:  # Can be modified to create straights
			potential += 0.3
		
		# Cards that could become pairs
		var rank_count = 0
		for other_card in cards:
			if other_card.get("value", 1) == rank:
				rank_count += 1
		
		if rank_count == 1:  # Single card that could become a pair
			potential += 0.2
	
	return potential

func apply_strategic_cheat(selected_cards: Array) -> Dictionary:
	if selected_cards.is_empty() or not hand_manager:
		return {"success": false, "type": ""}
	
	# Choose a random card to cheat with
	var card_id = selected_cards[randi() % selected_cards.size()]
	var card_info = hand_manager.get_card_info(card_id)
	
	if card_info.is_empty():
		return {"success": false, "type": ""}
	
	# Decide between rank and suit cheat
	var cheat_type = "rank" if randf() < 0.7 else "suit"
	
	if cheat_type == "rank":
		# Strategic rank modification
		var current_rank = card_info.get("value", 1)
		var new_rank = choose_strategic_rank(current_rank, selected_cards)
		
		if new_rank != current_rank:
			hand_manager.modify_card_rank(card_id, new_rank - current_rank)
			return {"success": true, "type": "rank"}
	else:
		# Strategic suit modification
		var new_suit = choose_strategic_suit(card_info, selected_cards)
		if new_suit != card_info.get("suit", "hearts"):
			hand_manager.modify_card_suit(card_id, new_suit)
			return {"success": true, "type": "suit"}
	
	return {"success": false, "type": ""}

func choose_strategic_rank(current_rank: int, cards: Array) -> int:
	# Look for opportunities to create pairs or improve straights
	var rank_counts = {}
	for card in cards:
		var rank = card.get("value", 1)
		rank_counts[rank] = rank_counts.get(rank, 0) + 1
	
	# Try to create a pair
	for rank in rank_counts.keys():
		if rank_counts[rank] == 1 and rank != current_rank:
			return rank
	
	# Try to improve to a high card
	if current_rank < 10:
		return current_rank + randi_range(1, 3)
	else:
		return current_rank - randi_range(1, 2)

func choose_strategic_suit(card_info: Dictionary, cards: Array) -> String:
	var suits = ["hearts", "diamonds", "clubs", "spades"]
	var current_suit = card_info.get("suit", "hearts")
	
	# Count suits in hand
	var suit_counts = {}
	for card in cards:
		var suit = card.get("suit", "hearts")
		suit_counts[suit] = suit_counts.get(suit, 0) + 1
	
	# Try to create a flush
	var best_suit = current_suit
	var best_count = 0
	
	for suit in suit_counts.keys():
		if suit != current_suit and suit_counts[suit] > best_count:
			best_count = suit_counts[suit]
			best_suit = suit
	
	return best_suit

func get_decision_reasoning(situation: Dictionary, should_cheat: bool, cheat_applied: bool) -> String:
	var reasoning = "Round " + str(situation.round) + " - "
	
	if situation.pressure_level > 0.7:
		reasoning += "High pressure situation. "
	elif situation.pressure_level < 0.3:
		reasoning += "Low pressure situation. "
	
	if should_cheat:
		if cheat_applied:
			reasoning += "Decided to cheat and succeeded."
		else:
			reasoning += "Decided to cheat but failed to apply."
	else:
		reasoning += "Playing honestly."
	
	return reasoning
