extends Node
class_name NetworkConfig

# Network configuration constants
const DEFAULT_PORT = 8910
const DEFAULT_ADDRESS = "127.0.0.1"
const MAX_PLAYERS = 2
const CONNECTION_TIMEOUT = 10.0

# Room configuration
const ROOM_CODE_LENGTH = 6
const ROOM_CODE_CHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

# Matchmaking configuration
const MAX_SEARCH_TIME = 30.0
const MAX_RETRIES = 3

# Scene paths
const MAIN_MENU_SCENE = "res://Scenes/MainMenu.tscn"
const CARD_GAME_SCENE = "res://Scenes/CardGame.tscn"
const MULTIPLAYER_LOBBY_SCENE = "res://Scenes/MultiplayerLobby.tscn"
const SEARCHING_UI_SCENE = "res://Scenes/SearchingUI.tscn"

# Generate a random room code
static func generate_room_code() -> String:
	var code = ""
	for i in range(ROOM_CODE_LENGTH):
		code += ROOM_CODE_CHARS[randi() % ROOM_CODE_CHARS.length()]
	return code

# Create a standardized error dialog
static func show_error_dialog(parent: Node, message: String) -> void:
	var dialog = AcceptDialog.new()
	dialog.dialog_text = message
	parent.add_child(dialog)
	dialog.popup_centered()

# Create a standardized message dialog with auto-removal
static func show_message_dialog(parent: Node, message: String, duration: float = 2.0) -> void:
	var dialog = AcceptDialog.new()
	dialog.dialog_text = message
	parent.add_child(dialog)
	dialog.popup_centered()
	
	# Auto-remove after duration
	await parent.get_tree().create_timer(duration).timeout
	if is_instance_valid(dialog):
		dialog.queue_free()
