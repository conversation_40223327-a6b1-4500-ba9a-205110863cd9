[gd_scene load_steps=4 format=3 uid="uid://clsm41n18duph"]

[ext_resource type="Script" uid="uid://voi2mox08brs" path="res://Scripts/SearchingUI.gd" id="1"]

[sub_resource type="Animation" id="Animation_1"]
resource_name = "searching"
loop_mode = 1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("VBoxContainer/Spinner:rotation")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [0.0, 6.28319]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_1"]
_data = {
&"searching": SubResource("Animation_1")
}

[node name="SearchingUI" type="Control" groups=["searching_ui"]]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0, 0, 0, 0.5)

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -150.0
offset_top = -100.0
offset_right = 150.0
offset_bottom = 100.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/separation = 20
alignment = 1

[node name="Label" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "Recherche d'un adversaire..."
horizontal_alignment = 1

[node name="Spinner" type="TextureRect" parent="VBoxContainer"]
custom_minimum_size = Vector2(64, 64)
layout_mode = 2
size_flags_horizontal = 4
expand_mode = 1
stretch_mode = 5

[node name="TimeLabel" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "Temps de recherche: 0.0 s"
horizontal_alignment = 1

[node name="CancelButton" type="Button" parent="VBoxContainer"]
custom_minimum_size = Vector2(120, 40)
layout_mode = 2
size_flags_horizontal = 4
text = "Annuler"

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_1")
}
