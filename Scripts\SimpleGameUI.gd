extends Control
class_name SimpleGameUI

# UI References - will be created dynamically
var phase_label: Label
var instruction_label: Label
var round_score_label: Label
var selected_info_label: Label

# Buttons
var proceed_button: Button
var exchange_button: Button
var modify_rank_button: Button
var modify_suit_button: Button
var validate_button: Button

# Hand display
var hand_container: HBoxContainer

# Main container
var main_vbox: VBoxContainer

# Game state
var game_manager: SimpleBestOfThree
var hand_manager: HandManager
var selected_card_ids: Array = []
var current_phase: SimpleBestOfThree.Phase

func _ready():
	create_ui_elements()
	setup_ui()

func create_ui_elements():
	# Create main container
	main_vbox = VBoxContainer.new()
	add_child(main_vbox)

	# Create labels
	phase_label = Label.new()
	phase_label.text = "CARD SELECTION"
	phase_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	main_vbox.add_child(phase_label)

	instruction_label = Label.new()
	instruction_label.text = "Select cards from your hand"
	instruction_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	main_vbox.add_child(instruction_label)

	round_score_label = Label.new()
	round_score_label.text = "Round 1/3 | Score: 0 - 0"
	round_score_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	main_vbox.add_child(round_score_label)

	selected_info_label = Label.new()
	selected_info_label.text = "Selected: 0/5 cards"
	selected_info_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	main_vbox.add_child(selected_info_label)

	# Create hand container
	hand_container = HBoxContainer.new()
	hand_container.alignment = BoxContainer.ALIGNMENT_CENTER
	main_vbox.add_child(hand_container)

	# Create buttons container
	var button_container = HBoxContainer.new()
	button_container.alignment = BoxContainer.ALIGNMENT_CENTER
	main_vbox.add_child(button_container)

	# Create buttons
	proceed_button = Button.new()
	proceed_button.text = "Proceed"
	button_container.add_child(proceed_button)

	exchange_button = Button.new()
	exchange_button.text = "Exchange"
	button_container.add_child(exchange_button)

	modify_rank_button = Button.new()
	modify_rank_button.text = "Modify Rank"
	button_container.add_child(modify_rank_button)

	modify_suit_button = Button.new()
	modify_suit_button.text = "Modify Suit"
	button_container.add_child(modify_suit_button)

	validate_button = Button.new()
	validate_button.text = "Validate"
	button_container.add_child(validate_button)

func setup_ui():
	# Connect buttons
	proceed_button.pressed.connect(_on_proceed_pressed)
	exchange_button.pressed.connect(_on_exchange_pressed)
	modify_rank_button.pressed.connect(_on_modify_rank_pressed)
	modify_suit_button.pressed.connect(_on_modify_suit_pressed)
	validate_button.pressed.connect(_on_validate_pressed)

	# Initial state
	update_ui_for_phase(SimpleBestOfThree.Phase.CARD_SELECTION)

func set_managers(game_mgr: SimpleBestOfThree, hand_mgr: HandManager):
	game_manager = game_mgr
	hand_manager = hand_mgr
	
	# Connect signals
	game_manager.phase_changed.connect(_on_phase_changed)
	game_manager.round_ended.connect(_on_round_ended)
	game_manager.game_ended.connect(_on_game_ended)

func _on_phase_changed(phase_name: String, instructions: String):
	phase_label.text = phase_name
	instruction_label.text = instructions
	
	# Update UI based on phase
	match phase_name:
		"CARD SELECTION":
			current_phase = SimpleBestOfThree.Phase.CARD_SELECTION
		"MODIFICATION":
			current_phase = SimpleBestOfThree.Phase.MODIFICATION
		"VALIDATION":
			current_phase = SimpleBestOfThree.Phase.VALIDATION
		"REVEAL":
			current_phase = SimpleBestOfThree.Phase.REVEAL
	
	update_ui_for_phase(current_phase)
	update_round_info()

func update_ui_for_phase(phase: SimpleBestOfThree.Phase):
	# Hide all buttons first
	proceed_button.visible = false
	exchange_button.visible = false
	modify_rank_button.visible = false
	modify_suit_button.visible = false
	validate_button.visible = false
	
	match phase:
		SimpleBestOfThree.Phase.CARD_SELECTION:
			proceed_button.visible = true
			proceed_button.disabled = selected_card_ids.is_empty()
		
		SimpleBestOfThree.Phase.MODIFICATION:
			exchange_button.visible = true
			modify_rank_button.visible = true
			modify_suit_button.visible = true
			validate_button.visible = true
			
			# Disable if already modified
			var has_modified = game_manager.has_player_modified(1) if game_manager else false
			exchange_button.disabled = has_modified
			modify_rank_button.disabled = has_modified
			modify_suit_button.disabled = has_modified
		
		SimpleBestOfThree.Phase.VALIDATION:
			# No buttons - waiting for other player
			pass
		
		SimpleBestOfThree.Phase.REVEAL:
			# No buttons - showing results
			pass

func update_round_info():
	if game_manager:
		var round_num = game_manager.get_current_round()
		var scores = game_manager.get_scores()
		round_score_label.text = "Round " + str(round_num) + "/3 | Score: " + str(scores.player1) + " - " + str(scores.player2)

func update_selected_info():
	var count = selected_card_ids.size()
	selected_info_label.text = "Selected: " + str(count) + "/5 cards"
	
	# Update proceed button
	if proceed_button:
		proceed_button.disabled = (count == 0)

# Card selection handling
func on_card_clicked(card_id: int):
	if current_phase != SimpleBestOfThree.Phase.CARD_SELECTION:
		return
	
	if selected_card_ids.has(card_id):
		# Deselect
		selected_card_ids.erase(card_id)
		print("Deselected card ", card_id)
	else:
		# Select
		if selected_card_ids.size() < 5:
			selected_card_ids.append(card_id)
			print("Selected card ", card_id)
		else:
			show_message("Maximum 5 cards allowed")
			return
	
	update_selected_info()
	update_hand_display()

func update_hand_display():
	# Clear existing display
	for child in hand_container.get_children():
		child.queue_free()
	
	if not hand_manager:
		return
	
	# Display player 1 hand
	for card in hand_manager.player1_hand:
		var card_button = Button.new()
		card_button.text = get_card_text(card)
		card_button.custom_minimum_size = Vector2(60, 80)
		
		# Highlight if selected
		if selected_card_ids.has(card.id):
			card_button.modulate = Color.YELLOW
		
		# Connect click
		card_button.pressed.connect(func(): on_card_clicked(card.id))
		
		hand_container.add_child(card_button)

func get_card_text(card: Dictionary) -> String:
	var rank = card.get("value", 1)
	var suit = card.get("suit", "hearts")
	
	var rank_text = ""
	match rank:
		1, 14:
			rank_text = "A"
		11:
			rank_text = "J"
		12:
			rank_text = "Q"
		13:
			rank_text = "K"
		_:
			rank_text = str(rank)
	
	var suit_symbol = ""
	match suit:
		"hearts":
			suit_symbol = "♥"
		"diamonds":
			suit_symbol = "♦"
		"clubs":
			suit_symbol = "♣"
		"spades":
			suit_symbol = "♠"
	
	return rank_text + suit_symbol

# Button handlers
func _on_proceed_pressed():
	if game_manager and not selected_card_ids.is_empty():
		game_manager.select_cards(1, selected_card_ids)
		game_manager.proceed_from_selection(1)

func _on_exchange_pressed():
	if selected_card_ids.size() != 1:
		show_message("Select exactly 1 card to exchange")
		return
	
	show_message("Exchange feature - select new card from remaining hand")
	# TODO: Implement exchange UI

func _on_modify_rank_pressed():
	if selected_card_ids.size() != 1:
		show_message("Select exactly 1 card to modify rank")
		return
	
	# Simple rank modification - increase by 1
	var card_id = selected_card_ids[0]
	if game_manager:
		game_manager.modify_card(1, card_id, "rank", 1)
		show_message("Card rank modified (ILLEGAL)")
		update_ui_for_phase(current_phase)

func _on_modify_suit_pressed():
	if selected_card_ids.size() != 1:
		show_message("Select exactly 1 card to modify suit")
		return
	
	# Simple suit modification - change to hearts
	var card_id = selected_card_ids[0]
	if game_manager:
		game_manager.modify_card(1, card_id, "suit", "hearts")
		show_message("Card suit modified (ILLEGAL)")
		update_ui_for_phase(current_phase)

func _on_validate_pressed():
	if game_manager:
		game_manager.validate_hand(1)

func _on_round_ended(_winner: int, results: Dictionary):
	var message = "Round ended!\n" + results.reason
	show_message(message)

func _on_game_ended(final_winner: int, scores: Dictionary):
	var message = ""
	if final_winner == 1:
		message = "YOU WIN THE GAME!"
	elif final_winner == 2:
		message = "AI WINS THE GAME!"
	else:
		message = "GAME TIED!"
	
	message += "\nFinal Score: " + str(scores.player1) + " - " + str(scores.player2)
	show_message(message)

func show_message(text: String):
	print("UI: " + text)
	# TODO: Implement proper message display
	if instruction_label:
		instruction_label.text = text

# Initialize hand display when managers are set
func refresh_display():
	if hand_manager:
		update_hand_display()
		update_selected_info()
		update_round_info()
