extends Node
class_name SimpleAI

# AI behavior
var cheat_probability: float = 0.4
var exchange_probability: float = 0.3

# Managers
var game_manager: SimpleBestOfThree
var hand_manager: HandManager

# AI state
var decision_timer: Timer
var has_acted_this_phase: bool = false

func _ready():
	# Create decision timer
	decision_timer = Timer.new()
	decision_timer.one_shot = true
	decision_timer.timeout.connect(_on_decision_timeout)
	add_child(decision_timer)

func set_managers(game_mgr: SimpleBestOfThree, hand_mgr: HandManager):
	game_manager = game_mgr
	hand_manager = hand_mgr
	
	# Connect to phase changes
	game_manager.phase_changed.connect(_on_phase_changed)

func _on_phase_changed(phase_name: String, instructions: String):
	has_acted_this_phase = false
	
	match phase_name:
		"CARD SELECTION":
			# Start timer for card selection
			decision_timer.wait_time = randf_range(1.0, 2.5)
			decision_timer.start()
		
		"MODIFICATION":
			# Start timer for modification decision
			decision_timer.wait_time = randf_range(1.5, 3.0)
			decision_timer.start()

func _on_decision_timeout():
	if has_acted_this_phase:
		return
	
	var current_phase = game_manager.get_current_phase()
	
	match current_phase:
		SimpleBestOfThree.Phase.CARD_SELECTION:
			make_card_selection()
		
		SimpleBestOfThree.Phase.MODIFICATION:
			make_modification_decision()

func make_card_selection():
	if has_acted_this_phase:
		return
	
	print("AI: Making card selection...")
	
	var available_cards = hand_manager.player2_hand
	if available_cards.is_empty():
		print("AI Error: No cards available")
		return
	
	# Select best combination
	var selected_cards = select_best_combination(available_cards)
	
	if not selected_cards.is_empty():
		# Submit selection
		game_manager.select_cards(2, selected_cards)
		game_manager.proceed_from_selection(2)
		has_acted_this_phase = true
		print("AI: Selected ", selected_cards.size(), " cards and proceeded")
	else:
		print("AI Error: Could not select cards")

func select_best_combination(available_cards: Array) -> Array:
	var best_combination = []
	var best_score = -1
	
	# Try combinations of different sizes
	for size in range(1, min(6, available_cards.size() + 1)):
		var combinations = generate_combinations(available_cards, size)
		
		for combo in combinations:
			var score = evaluate_combination(combo)
			if score > best_score:
				best_score = score
				best_combination = combo
	
	# Extract card IDs
	var card_ids = []
	for card in best_combination:
		card_ids.append(card.id)
	
	return card_ids

func generate_combinations(cards: Array, size: int) -> Array:
	if size == 0 or cards.is_empty():
		return [[]]
	
	if size > cards.size():
		return []
	
	var result = []
	
	# Simple combination generation for small sizes
	if size == 1:
		for card in cards:
			result.append([card])
	elif size == 2:
		for i in range(cards.size()):
			for j in range(i + 1, cards.size()):
				result.append([cards[i], cards[j]])
	elif size == 3:
		for i in range(cards.size()):
			for j in range(i + 1, cards.size()):
				for k in range(j + 1, cards.size()):
					result.append([cards[i], cards[j], cards[k]])
	else:
		# For larger sizes, just take first N cards (simplified)
		result.append(cards.slice(0, size))
	
	return result

func evaluate_combination(cards: Array) -> float:
	if cards.is_empty():
		return 0.0
	
	# Convert to evaluation format
	var eval_cards = CardUtils.convert_cards_for_evaluation(cards)
	
	# Get poker hand value
	var poker_evaluator = load("res://Scripts/PokerHand.gd").new()
	var hand_value = poker_evaluator.evaluate_hand(eval_cards)
	var base_score = hand_manager.get_hand_score(hand_value)
	
	# Prefer smaller hands for flexibility
	var size_bonus = 0.0
	if cards.size() <= 3:
		size_bonus = 0.5
	
	return float(base_score) + size_bonus

func make_modification_decision():
	if has_acted_this_phase:
		return
	
	print("AI: Making modification decision...")
	
	# Calculate pressure level
	var current_round = game_manager.get_current_round()
	var scores = game_manager.get_scores()
	var pressure = calculate_pressure(current_round, scores)
	
	# Decide action
	var action = decide_action(pressure)
	
	match action:
		"exchange":
			attempt_exchange()
		"modify":
			attempt_modification()
		"validate":
			validate_immediately()
	
	has_acted_this_phase = true

func calculate_pressure(round: int, scores: Dictionary) -> float:
	var ai_score = scores.player2
	var player_score = scores.player1
	var pressure = 0.0
	
	# Round pressure
	if round == 3:
		pressure += 0.4
	elif round == 2:
		pressure += 0.2
	
	# Score pressure
	if player_score > ai_score:
		pressure += 0.3
	
	# Must-win situation
	if round == 3 and player_score > ai_score:
		pressure += 0.5
	
	return clamp(pressure, 0.0, 1.0)

func decide_action(pressure: float) -> String:
	var adjusted_cheat_prob = cheat_probability + (pressure * 0.3)
	var adjusted_exchange_prob = exchange_probability
	
	var rand_value = randf()
	
	if rand_value < adjusted_exchange_prob:
		return "exchange"
	elif rand_value < adjusted_exchange_prob + adjusted_cheat_prob:
		return "modify"
	else:
		return "validate"

func attempt_exchange():
	print("AI: Attempting exchange")
	
	var selected_cards = game_manager.get_selected_cards(2)
	if selected_cards.is_empty():
		validate_immediately()
		return
	
	# Find worst card in selection
	var cards_info = hand_manager.get_cards_info(selected_cards)
	var worst_card = find_worst_card(cards_info)
	
	# Find best replacement from remaining cards
	var remaining_cards = get_remaining_cards()
	var best_replacement = find_best_card(remaining_cards)
	
	if worst_card.is_empty() or best_replacement.is_empty():
		validate_immediately()
		return
	
	# Perform exchange
	if game_manager.exchange_card(2, worst_card.id, best_replacement.id):
		print("AI: Successfully exchanged card")
	
	validate_immediately()

func attempt_modification():
	print("AI: Attempting modification")
	
	var selected_cards = game_manager.get_selected_cards(2)
	if selected_cards.is_empty():
		validate_immediately()
		return
	
	# Choose random card to modify
	var card_id = selected_cards[randi() % selected_cards.size()]
	
	# Choose modification type
	if randf() < 0.7:
		# Modify rank
		var rank_change = [-2, -1, 1, 2][randi() % 4]
		game_manager.modify_card(2, card_id, "rank", rank_change)
		print("AI: Modified card rank")
	else:
		# Modify suit
		var suits = ["hearts", "diamonds", "clubs", "spades"]
		var new_suit = suits[randi() % suits.size()]
		game_manager.modify_card(2, card_id, "suit", new_suit)
		print("AI: Modified card suit")
	
	validate_immediately()

func validate_immediately():
	print("AI: Validating hand")
	game_manager.validate_hand(2)

func find_worst_card(cards: Array) -> Dictionary:
	if cards.is_empty():
		return {}
	
	var worst = cards[0]
	for card in cards:
		if card.get("value", 1) < worst.get("value", 1):
			worst = card
	
	return worst

func find_best_card(cards: Array) -> Dictionary:
	if cards.is_empty():
		return {}
	
	var best = cards[0]
	for card in cards:
		if card.get("value", 1) > best.get("value", 1):
			best = card
	
	return best

func get_remaining_cards() -> Array:
	var remaining = []
	var all_cards = hand_manager.player2_hand
	var selected_cards = game_manager.get_selected_cards(2)
	
	for card in all_cards:
		if not selected_cards.has(card.id):
			remaining.append(card)
	
	return remaining
