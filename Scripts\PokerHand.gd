extends Node

# Évalue la main de poker sélectionnée
func evaluate_hand(selected_cards):
	if selected_cards.size() == 0:
		return "Aucune carte sélectionnée"

	insertion_sort(selected_cards)
	
	# Analyser les mains possibles et retourner le nom de la combinaison trouvée
	if selected_cards.size() >= 5:
		if is_straight_flush(selected_cards):
			return "Quinte Flush"
		elif is_four_of_a_kind(selected_cards):
			return "Carré de " + get_four_of_a_kind_name(selected_cards)
		elif is_full_house(selected_cards):
			return "Full House de " + get_full_house_names(selected_cards)
		elif is_flush(selected_cards):
			return "Couleur"
		elif is_straight(selected_cards):
			return "Suite"
		elif is_three_of_a_kind(selected_cards):
			return "Brelan de " + get_three_of_a_kind_name(selected_cards)
		elif is_two_pair(selected_cards):
			return "Double Paire: " + get_two_pair_names(selected_cards)
		elif is_one_pair(selected_cards):
			return "Paire de " + get_pair_name(selected_cards)
	elif selected_cards.size() == 4:
		if is_four_of_a_kind(selected_cards):
			return "Carré de " + get_four_of_a_kind_name(selected_cards)
		elif is_three_of_a_kind(selected_cards):
			return "Brelan de " + get_three_of_a_kind_name(selected_cards)
		elif is_two_pair(selected_cards):
			return "Double Paire: " + get_two_pair_names(selected_cards)
		elif is_one_pair(selected_cards):
			return "Paire de " + get_pair_name(selected_cards)
	elif selected_cards.size() == 3:
		if is_three_of_a_kind(selected_cards):
			return "Brelan de " + get_three_of_a_kind_name(selected_cards)
		elif is_one_pair(selected_cards):
			return "Paire de " + get_pair_name(selected_cards)
	elif selected_cards.size() == 2:
		if is_one_pair(selected_cards):
			return "Paire de " + get_pair_name(selected_cards)
	
	# Trouver la carte la plus haute après le tri
	var highest_card = find_highest_card(selected_cards)
	return "Carte Haute: " + get_card_name(highest_card["rank"])

# Tri par insertion stable en ordre décroissant
func insertion_sort(cards):
	for i in range(1, cards.size()):
		var key = cards[i]
		var j = i - 1
		while j >= 0 and compare_cards(cards[j], key) < 0:
			cards[j + 1] = cards[j]
			j -= 1
		cards[j + 1] = key

# Fonction de comparaison de 2 cartes par rang
func compare_cards(a, b):
	var a_rank = a["rank"]
	var b_rank = b["rank"]

	if a_rank == 1:
		a_rank = 14  # Traite l'As comme 14
	if b_rank == 1:
		b_rank = 14  # Traite l'As comme 14
	
	if a_rank < b_rank:
		return -1
	elif a_rank > b_rank:
		return 1
	else:
		return 0

# Trouve la carte avec le rang le plus élevé dans la liste
func find_highest_card(cards):
	var highest_card = cards[0]
	for card in cards:
		var rank = card["rank"]
		if rank == 1:
			rank = 14  # Considérer l'As comme la carte la plus haute
		var highest_rank = highest_card["rank"]
		if highest_rank == 1:
			highest_rank = 14  # Considérer l'As comme la carte la plus haute
		if rank > highest_rank:
			highest_card = card
	return highest_card

func is_flush(cards):
	var suit = cards[0].suit
	for card in cards:
		if card.suit != suit:
			return false
	return true

func is_straight(cards):
	# Crée une copie des rangs des cartes
	var ranks = []
	for card in cards:
		ranks.append(card.rank)
	
	# Trie les rangs
	ranks.sort()
	
	# Vérification pour une suite avec un As bas (As, 2, 3, 4, 5)
	if ranks == [1, 2, 3, 4, 5]:
		return true

	# Vérification pour une suite avec un As haut (10, Valet, Dame, Roi, As)
	if ranks == [10, 11, 12, 13, 1]:
		return true

	# Traite l'As comme 14 si nécessaire
	for i in range(len(ranks)):
		if ranks[i] == 1:
			ranks[i] = 14
	
	# Trie les rangs à nouveau après avoir traité l'As
	ranks.sort()

	# Vérifie si les rangs sont consécutifs
	for i in range(len(ranks) - 1):
		if ranks[i + 1] - ranks[i] != 1:
			return false
	
	return true

func is_straight_flush(cards):
	# Doit satisfaire les conditions de `is_straight` et `is_flush`
	return is_flush(cards) and is_straight(cards)

# Vérifie si quatre cartes sont du même rang
func is_four_of_a_kind(cards):
	var ranks = {}
	for card in cards:
		ranks[card["rank"]] = ranks.get(card["rank"], 0) + 1
	return 4 in ranks.values()

# Vérifie si trois cartes sont d'un rang et deux d'un autre rang
func is_full_house(cards):
	var ranks = {}
	for card in cards:
		ranks[card["rank"]] = ranks.get(card["rank"], 0) + 1
	return 3 in ranks.values() and 2 in ranks.values()

# Vérifie si trois cartes sont du même rang
func is_three_of_a_kind(cards):
	var ranks = {}
	for card in cards:
		ranks[card["rank"]] = ranks.get(card["rank"], 0) + 1
	return 3 in ranks.values()

# Vérifie si deux paires de cartes sont du même rang
func is_two_pair(cards):
	var ranks = {}
	var pair_count = 0
	for card in cards:
		ranks[card["rank"]] = ranks.get(card["rank"], 0) + 1
	for count in ranks.values():
		if count == 2:
			pair_count += 1
	return pair_count == 2

# Vérifie si deux cartes sont du même rang
func is_one_pair(cards):
	var ranks = {}
	for card in cards:
		ranks[card["rank"]] = ranks.get(card["rank"], 0) + 1
	return 2 in ranks.values()

# Convertit la valeur de la carte en un nom lisible
func get_card_name(rank: int) -> String:
	match rank:
		1:
			return "As"
		11:
			return "Valet"
		12:
			return "Dame"
		13:
			return "Roi"
		14:
			return "As"  # Pour les As traités comme 14
		_:
			return str(rank)

# Retourne le nom de la paire
func get_pair_name(cards):
	var ranks = {}
	for card in cards:
		ranks[card["rank"]] = ranks.get(card["rank"], 0) + 1
	for rank in ranks.keys():
		if ranks[rank] == 2:
			return get_card_name(rank)
	return ""

# Retourne les noms des deux paires
func get_two_pair_names(cards):
	var ranks = {}
	var pairs = []
	for card in cards:
		ranks[card["rank"]] = ranks.get(card["rank"], 0) + 1
	for rank in ranks.keys():
		if ranks[rank] == 2:
			pairs.append(get_card_name(rank))
	return String(", ").join(pairs)

# Retourne le nom du carré
func get_four_of_a_kind_name(cards):
	var ranks = {}
	for card in cards:
		ranks[card["rank"]] = ranks.get(card["rank"], 0) + 1
	for rank in ranks.keys():
		if ranks[rank] == 4:
			return get_card_name(rank)
	return ""

# Retourne les noms du full house
func get_full_house_names(cards):
	var ranks = {}
	var three_of_a_kind = ""
	var pair = ""
	for card in cards:
		ranks[card["rank"]] = ranks.get(card["rank"], 0) + 1
	for rank in ranks.keys():
		if ranks[rank] == 3:
			three_of_a_kind = get_card_name(rank)
		elif ranks[rank] == 2:
			pair = get_card_name(rank)
	return three_of_a_kind + " et " + pair

# Retourne le nom du brelan
func get_three_of_a_kind_name(cards):
	var ranks = {}
	for card in cards:
		ranks[card["rank"]] = ranks.get(card["rank"], 0) + 1
	for rank in ranks.keys():
		if ranks[rank] == 3:
			return get_card_name(rank)
	return ""
