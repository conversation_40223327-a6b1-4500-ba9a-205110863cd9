extends Control

signal search_cancelled

@onready var cancel_button = $VBoxContainer/CancelButton
@onready var time_label = $VBoxContainer/TimeLabel
@onready var animation_player = $AnimationPlayer

var search_time = 0.0

# Ajouter un timeout pour la recherche
const MAX_SEARCH_TIME = 60.0

func _ready():
    cancel_button.pressed.connect(_on_cancel_pressed)
    animation_player.play("searching")

func _process(delta):
    search_time += delta
    time_label.text = "Temps de recherche: %.1f s" % search_time
    
    if search_time >= MAX_SEARCH_TIME:
        emit_signal("search_cancelled")
        queue_free()

func _on_cancel_pressed():
    emit_signal("search_cancelled")
    queue_free()

func _exit_tree():
    animation_player.stop()