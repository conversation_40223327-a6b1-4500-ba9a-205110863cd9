extends Control
class_name VisualCard

signal card_clicked(card_id: int)
signal card_hovered(card_id: int)
signal card_unhovered(card_id: int)

# Card data
var card_data: Dictionary
var card_id: int
var is_selected: bool = false
var is_hovered: bool = false

# Visual components
@onready var card_texture: TextureRect
@onready var card_button: Button
@onready var selection_glow: Control
@onready var hover_effect: Control

# Animation
var tween: Tween
var original_position: Vector2
var original_scale: Vector2 = Vector2.ONE

func _init(card_info: Dictionary):
	card_data = card_info
	card_id = card_info.get("id", 0)
	custom_minimum_size = Vector2(86, 122)  # CARD_WIDTH, CARD_HEIGHT
	
func _ready():
	setup_visual_components()
	setup_animations()
	connect_signals()

func setup_visual_components():
	# Main card texture
	card_texture = TextureRect.new()
	card_texture.expand_mode = TextureRect.EXPAND_FIT_WIDTH_PROPORTIONAL
	card_texture.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_CENTERED
	card_texture.size = Vector2(86, 122)  # CARD_WIDTH, CARD_HEIGHT
	add_child(card_texture)

	# Invisible button for interaction
	card_button = Button.new()
	card_button.flat = true
	card_button.size = Vector2(86, 122)  # CARD_WIDTH, CARD_HEIGHT
	card_button.modulate = Color.TRANSPARENT
	add_child(card_button)
	
	# Selection glow effect
	selection_glow = Control.new()
	selection_glow.size = Vector2(96, 132)  # CARD_WIDTH + 10, CARD_HEIGHT + 10
	selection_glow.position = Vector2(-5, -5)
	selection_glow.visible = false
	add_child(selection_glow)
	
	# Load card texture
	load_card_texture()

func load_card_texture():
	var texture_path = get_card_texture_path()
	var texture = load(texture_path)
	if texture:
		card_texture.texture = texture
	else:
		# Fallback to text display
		create_text_fallback()

func get_card_texture_path() -> String:
	var suit = card_data.get("suit", "hearts")
	var value = card_data.get("value", 1)
	
	# Map to texture files
	match suit.to_lower():
		"hearts":
			return "res://Assets/cards/Top-Down/Cards/Hearts.png"
		"diamonds":
			return "res://Assets/cards/Top-Down/Cards/Diamonds.png"
		"clubs":
			return "res://Assets/cards/Top-Down/Cards/Clubs.png"
		"spades":
			return "res://Assets/cards/Top-Down/Cards/Spades.png"
		_:
			return "res://Assets/cards/Top-Down/Cards/Hearts.png"

func create_text_fallback():
	# Create text label as fallback
	var label = Label.new()
	label.text = get_card_text()
	label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	label.size = Vector2(86, 122)  # CARD_WIDTH, CARD_HEIGHT
	label.add_theme_font_size_override("font_size", 16)
	
	# Style the label to look like a card
	var style_box = StyleBoxFlat.new()
	style_box.bg_color = Color.WHITE
	style_box.border_width_left = 2
	style_box.border_width_right = 2
	style_box.border_width_top = 2
	style_box.border_width_bottom = 2
	style_box.border_color = Color.BLACK
	style_box.corner_radius_top_left = 8
	style_box.corner_radius_top_right = 8
	style_box.corner_radius_bottom_left = 8
	style_box.corner_radius_bottom_right = 8
	
	label.add_theme_stylebox_override("normal", style_box)
	add_child(label)

func get_card_text() -> String:
	var rank = card_data.get("value", 1)
	var suit = card_data.get("suit", "hearts")
	
	var rank_text = ""
	match rank:
		1: rank_text = "A"
		11: rank_text = "J"
		12: rank_text = "Q"
		13: rank_text = "K"
		_: rank_text = str(rank)
	
	var suit_symbol = ""
	match suit:
		"hearts": suit_symbol = "♥"
		"diamonds": suit_symbol = "♦"
		"clubs": suit_symbol = "♣"
		"spades": suit_symbol = "♠"
	
	return rank_text + suit_symbol

func setup_animations():
	original_position = position
	original_scale = scale

func connect_signals():
	if card_button:
		card_button.pressed.connect(_on_card_pressed)
		card_button.mouse_entered.connect(_on_card_hovered)
		card_button.mouse_exited.connect(_on_card_unhovered)

func _on_card_pressed():
	emit_signal("card_clicked", card_id)

func _on_card_hovered():
	if not is_hovered:
		is_hovered = true
		animate_hover(true)
		emit_signal("card_hovered", card_id)

func _on_card_unhovered():
	if is_hovered:
		is_hovered = false
		animate_hover(false)
		emit_signal("card_unhovered", card_id)

func set_selected(selected: bool):
	if is_selected != selected:
		is_selected = selected
		animate_selection(selected)

func animate_hover(hover: bool):
	if tween:
		tween.kill()
	tween = create_tween()
	tween.set_parallel(true)
	
	if hover:
		# Hover effect: lift up and scale slightly
		tween.tween_property(self, "position:y", original_position.y - 30.0, 0.2)  # HOVER_LIFT, ANIMATION_DURATION
		tween.tween_property(self, "scale", Vector2(1.1, 1.1), 0.2)  # HOVER_SCALE, ANIMATION_DURATION
		tween.tween_property(self, "modulate", Color(1.1, 1.1, 1.1, 1.0), 0.2)  # ANIMATION_DURATION
	else:
		# Return to normal (unless selected)
		if not is_selected:
			tween.tween_property(self, "position:y", original_position.y, 0.2)  # ANIMATION_DURATION
			tween.tween_property(self, "scale", original_scale, 0.2)  # ANIMATION_DURATION
			tween.tween_property(self, "modulate", Color.WHITE, 0.2)  # ANIMATION_DURATION

func animate_selection(selected: bool):
	if tween:
		tween.kill()
	tween = create_tween()
	tween.set_parallel(true)
	
	if selected:
		# Selection effect: lift higher and change color
		tween.tween_property(self, "position:y", original_position.y - 50.0, 0.2)  # SELECTION_LIFT, ANIMATION_DURATION
		tween.tween_property(self, "scale", Vector2(1.15, 1.15), 0.2)  # SELECTION_SCALE, ANIMATION_DURATION
		tween.tween_property(self, "modulate", Color(1.0, 0.8, 0.2, 1.0), 0.2)  # SELECTED_COLOR, ANIMATION_DURATION

		# Show selection glow
		if selection_glow:
			selection_glow.visible = true
	else:
		# Return to normal
		tween.tween_property(self, "position:y", original_position.y, 0.2)  # ANIMATION_DURATION
		tween.tween_property(self, "scale", original_scale, 0.2)  # ANIMATION_DURATION
		tween.tween_property(self, "modulate", Color.WHITE, 0.2)  # ANIMATION_DURATION
		
		# Hide selection glow
		if selection_glow:
			selection_glow.visible = false

func animate_modification():
	# Special animation for when card is modified
	if tween:
		tween.kill()
	tween = create_tween()
	tween.set_parallel(true)
	
	# Flash red to indicate modification
	tween.tween_property(self, "modulate", Color.RED, 0.1)
	tween.tween_property(self, "modulate", Color.WHITE, 0.1)
	tween.tween_property(self, "modulate", Color.RED, 0.1)
	tween.tween_property(self, "modulate", GameConfig.SELECTED_COLOR if is_selected else Color.WHITE, 0.1)

func update_card_data(new_data: Dictionary):
	card_data = new_data
	load_card_texture()
	
	# If card was modified, show animation
	if card_data.get("modified", false):
		animate_modification()

func get_card_data() -> Dictionary:
	return card_data

func cleanup():
	if tween:
		tween.kill()
	queue_free()
