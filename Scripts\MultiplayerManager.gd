extends Node
class_name MultiplayerManager

const NetworkConfig = preload("res://Scripts/NetworkConfig.gd")
const CardUtils = preload("res://Scripts/CardUtils.gd")

signal game_state_updated(state: Dictionary)

var peer := ENetMultiplayerPeer.new()

func initialize_multiplayer_game() -> void:
    var initial_state := {
        "player1_hand": CardUtils.generate_random_hand(8),
        "player2_hand": CardUtils.generate_random_hand(8),
        "current_turn": 1,
        "game_state": "playing"
    }
    rpc("sync_game_state", initial_state)
    emit_signal("game_state_updated", initial_state)

@rpc("any_peer", "reliable")
func sync_game_state(state: Dictionary) -> void:
    if not multiplayer.is_server():
        emit_signal("game_state_updated", state)

func generate_random_hand(size: int) -> Array:
    var hand := []
    for i in range(size):
        hand.append({
            "id": randi(),
            "value": randi() % 13 + 1,
            "suit": ["hearts", "diamonds", "clubs", "spades"][randi() % 4]
        })
    return hand