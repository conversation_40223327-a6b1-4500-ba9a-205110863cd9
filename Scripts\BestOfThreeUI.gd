extends Control
class_name BestOfThreeUI

signal cards_selected(card_ids: Array)
signal card_exchanged(old_card_id: int, new_card_id: int)
signal card_modified(card_id: int, modification_type: String, new_value)
signal hand_validated()

# UI References
@onready var phase_label: Label = $PhaseInfo/PhaseLabel
@onready var round_label: Label = $PhaseInfo/RoundLabel
@onready var score_label: Label = $PhaseInfo/ScoreLabel
@onready var instruction_label: Label = $PhaseInfo/InstructionLabel

# Card selection UI
@onready var hand_container: Control = $HandContainer
@onready var selected_cards_container: Control = $SelectedCardsContainer

# Action buttons
@onready var action_buttons: Control = $ActionButtons
@onready var proceed_button: Button = $ActionButtons/ProceedButton
@onready var exchange_button: Button = $ActionButtons/ExchangeButton
@onready var modify_rank_button: Button = $ActionButtons/ModifyRankButton
@onready var modify_suit_button: Button = $ActionButtons/ModifySuitButton
@onready var validate_button: Button = $ActionButtons/ValidateButton

# Modification panels
@onready var exchange_panel: Panel = $ExchangePanel
@onready var rank_modification_panel: Panel = $RankModificationPanel
@onready var suit_modification_panel: Panel = $SuitModificationPanel

# Game state
var current_phase: BestOfThreeManager.GamePhase
var selected_card_ids: Array = []
var max_selected_cards: int = 5
var has_modified: bool = false
var modification_used: bool = false

# Managers
var best_of_three_manager: BestOfThreeManager
var hand_manager: HandManager

func _ready():
	setup_ui_connections()
	hide_all_panels()

func setup_ui_connections():
	proceed_button.pressed.connect(_on_proceed_pressed)
	exchange_button.pressed.connect(_on_exchange_pressed)
	modify_rank_button.pressed.connect(_on_modify_rank_pressed)
	modify_suit_button.pressed.connect(_on_modify_suit_pressed)
	validate_button.pressed.connect(_on_validate_pressed)

func set_managers(bot_manager: BestOfThreeManager, hand_mgr: HandManager):
	best_of_three_manager = bot_manager
	hand_manager = hand_mgr
	
	# Connect to manager signals
	best_of_three_manager.round_started.connect(_on_round_started)
	best_of_three_manager.phase_changed.connect(_on_phase_changed)
	best_of_three_manager.round_ended.connect(_on_round_ended)

func _on_round_started(round_number: int):
	reset_for_new_round()
	update_round_info()

func _on_phase_changed(new_phase: String):
	match new_phase:
		"card_selection":
			current_phase = BestOfThreeManager.GamePhase.CARD_SELECTION
			setup_card_selection_phase()
		"modification":
			current_phase = BestOfThreeManager.GamePhase.MODIFICATION
			setup_modification_phase()
		"validation":
			current_phase = BestOfThreeManager.GamePhase.VALIDATION
			setup_validation_phase()
		"reveal":
			current_phase = BestOfThreeManager.GamePhase.REVEAL
			setup_reveal_phase()
		"round_end":
			current_phase = BestOfThreeManager.GamePhase.ROUND_END
			setup_round_end_phase()

func _on_round_ended(winner: int, results: Dictionary):
	show_round_results(winner, results)

func reset_for_new_round():
	selected_card_ids.clear()
	has_modified = false
	modification_used = false
	hide_all_panels()

func update_round_info():
	if best_of_three_manager:
		var round_num = best_of_three_manager.get_current_round()
		var scores = best_of_three_manager.get_scores()
		round_label.text = "Round " + str(round_num) + "/3"
		score_label.text = "Score: " + str(scores.player1) + " - " + str(scores.player2)

func setup_card_selection_phase():
	phase_label.text = "CARD SELECTION"
	instruction_label.text = "Select 1-5 cards from your hand, then proceed"
	
	# Show only proceed button
	proceed_button.visible = true
	proceed_button.disabled = true
	exchange_button.visible = false
	modify_rank_button.visible = false
	modify_suit_button.visible = false
	validate_button.visible = false
	
	update_proceed_button()

func setup_modification_phase():
	phase_label.text = "MODIFICATION"
	instruction_label.text = "Exchange a card (legal) or modify a card (illegal), then validate"
	
	# Show modification buttons
	proceed_button.visible = false
	exchange_button.visible = true
	modify_rank_button.visible = true
	modify_suit_button.visible = true
	validate_button.visible = true
	
	# Enable/disable based on state
	exchange_button.disabled = modification_used
	modify_rank_button.disabled = modification_used
	modify_suit_button.disabled = modification_used
	validate_button.disabled = false

func setup_validation_phase():
	phase_label.text = "VALIDATION"
	instruction_label.text = "Waiting for opponent to validate..."
	
	# Disable all action buttons
	proceed_button.visible = false
	exchange_button.visible = false
	modify_rank_button.visible = false
	modify_suit_button.visible = false
	validate_button.visible = false

func setup_reveal_phase():
	phase_label.text = "REVEAL"
	instruction_label.text = "Revealing hands..."
	
	# Hide all buttons
	action_buttons.visible = false

func setup_round_end_phase():
	phase_label.text = "ROUND END"
	instruction_label.text = "Round completed"

# Button handlers
func _on_proceed_pressed():
	if current_phase == BestOfThreeManager.GamePhase.CARD_SELECTION:
		if not selected_card_ids.is_empty():
			best_of_three_manager.select_cards(1, selected_card_ids)
			best_of_three_manager.proceed_to_modification()

func _on_exchange_pressed():
	if selected_card_ids.size() != 1:
		show_message("Select exactly one card to exchange")
		return
	
	show_exchange_panel()

func _on_modify_rank_pressed():
	if selected_card_ids.size() != 1:
		show_message("Select exactly one card to modify its rank")
		return
	
	show_rank_modification_panel()

func _on_modify_suit_pressed():
	if selected_card_ids.size() != 1:
		show_message("Select exactly one card to modify its suit")
		return
	
	show_suit_modification_panel()

func _on_validate_pressed():
	best_of_three_manager.validate_hand(1)

# Panel management
func hide_all_panels():
	if exchange_panel:
		exchange_panel.visible = false
	if rank_modification_panel:
		rank_modification_panel.visible = false
	if suit_modification_panel:
		suit_modification_panel.visible = false

func show_exchange_panel():
	hide_all_panels()
	exchange_panel.visible = true
	# TODO: Populate with available cards

func show_rank_modification_panel():
	hide_all_panels()
	rank_modification_panel.visible = true
	# TODO: Populate with rank options

func show_suit_modification_panel():
	hide_all_panels()
	suit_modification_panel.visible = true
	# TODO: Populate with suit options

# Card selection handling
func on_card_selected(card_id: int):
	if current_phase != BestOfThreeManager.GamePhase.CARD_SELECTION:
		return
	
	if selected_card_ids.has(card_id):
		selected_card_ids.erase(card_id)
	else:
		if selected_card_ids.size() < max_selected_cards:
			selected_card_ids.append(card_id)
		else:
			show_message("Maximum " + str(max_selected_cards) + " cards allowed")
			return
	
	update_selected_cards_display()
	update_proceed_button()

func update_proceed_button():
	if proceed_button:
		proceed_button.disabled = selected_card_ids.is_empty()

func update_selected_cards_display():
	# TODO: Update visual display of selected cards
	if instruction_label:
		instruction_label.text = "Selected: " + str(selected_card_ids.size()) + "/" + str(max_selected_cards) + " cards"

# Exchange handling
func perform_exchange(old_card_id: int, new_card_id: int):
	if best_of_three_manager.exchange_card(1, old_card_id, new_card_id):
		modification_used = true
		has_modified = true
		hide_all_panels()
		update_modification_buttons()
		show_message("Card exchanged (legal action)")
	else:
		show_message("Exchange failed")

# Modification handling
func perform_rank_modification(card_id: int, new_rank: int):
	if best_of_three_manager.modify_card(1, card_id, "rank", new_rank):
		modification_used = true
		has_modified = true
		hide_all_panels()
		update_modification_buttons()
		show_message("Card rank modified (illegal action)")
	else:
		show_message("Modification failed")

func perform_suit_modification(card_id: int, new_suit: String):
	if best_of_three_manager.modify_card(1, card_id, "suit", new_suit):
		modification_used = true
		has_modified = true
		hide_all_panels()
		update_modification_buttons()
		show_message("Card suit modified (illegal action)")
	else:
		show_message("Modification failed")

func update_modification_buttons():
	exchange_button.disabled = modification_used
	modify_rank_button.disabled = modification_used
	modify_suit_button.disabled = modification_used

func show_round_results(winner: int, results: Dictionary):
	var result_text = ""
	if winner == 1:
		result_text = "You won this round!"
	elif winner == 2:
		result_text = "Opponent won this round!"
	else:
		result_text = "Round tied!"
	
	result_text += "\n" + results.reason
	show_message(result_text)

func show_message(text: String):
	# TODO: Implement proper message display
	print("UI Message: " + text)
	if instruction_label:
		instruction_label.text = text

# Getters
func get_selected_cards() -> Array:
	return selected_card_ids.duplicate()

func is_modification_used() -> bool:
	return modification_used
