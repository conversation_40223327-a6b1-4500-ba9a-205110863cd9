extends Control

@onready var hand_manager: HandManager = $HandManager
@onready var ui_manager: CardGameUI = $UIManager
@onready var multiplayer_manager: MultiplayerManager = $MultiplayerManager
@onready var effect_manager: EffectManager = $EffectManager

# Buttons
@onready var menu_button: Button = $MenuButton
@onready var best_hand_button: Button = $ButtonsContainer/BestHandButton
@onready var cheat_rank_button: Button = $ButtonsContainer/CheatRankButton
@onready var cheat_color_button: Button = $ButtonsContainer/CheatColorButton
@onready var validate_hand_button: Button = $ButtonsContainer/ValidateHandButton

# Panels
@onready var change_rank_panel: Panel = $ChangeRankPanel
@onready var change_color_panel: Panel = $ChangeColorPanel
@onready var winner_screen: Panel = $WinnerScreen

# Variables d'état du jeu
var is_multiplayer := false
var game_state := "setup"  # setup, playing, round_end, game_end
var current_turn := 1
var selected_card_for_cheat = null
var cheat_rank_used := false
var cheat_color_used := false

# Variables for turn-based gameplay
var active_player := 1  # 1 or 2
var player1_action_taken := false  # Has player 1 played or cheated this turn?
var player2_action_taken := false  # Has player 2 played or cheated this turn?
var player1_has_played := false  # Has player 1 played their hand?
var player2_has_played := false  # Has player 2 played their hand?
var player1_played_hand := []  # Cards played by player 1
var player2_played_hand := []  # Cards played by player 2

# Score et rounds
var current_round := 1
var max_rounds := 3
var player1_score := 0
var player2_score := 0

func _ready() -> void:
	hand_manager.connect("hand_updated", _on_hand_updated)
	multiplayer_manager.connect("game_state_updated", _on_game_state_updated)

	# S'assurer que les gestionnaires sont bien initialisés
	if not hand_manager or not ui_manager or not effect_manager:
		push_error("Required managers not found")
		return

	# Connect button signals
	menu_button.pressed.connect(_on_menu_button_pressed)
	best_hand_button.pressed.connect(_on_best_hand_button_pressed)
	cheat_rank_button.pressed.connect(_on_cheat_rank_button_pressed)
	cheat_color_button.pressed.connect(_on_cheat_color_button_pressed)
	validate_hand_button.pressed.connect(_on_validate_hand_button_pressed)

	# Les anciens boutons ont été supprimés, nous utilisons maintenant des cartes visuelles

	# Hide panels initially
	change_rank_panel.visible = false
	change_color_panel.visible = false
	winner_screen.visible = false

	# Configurer la détection du clic droit global
	set_process_input(true)

	initialize_game()

func initialize_game() -> void:
	if is_multiplayer:
		multiplayer_manager.initialize_multiplayer_game()
	else:
		hand_manager.initialize_solo_game()

	# Determine first player (coin flip)
	determine_first_player()

	# Update interface to show active player
	update_game_ui()

	# Change game state
	game_state = "playing"

	# If AI starts, make it play immediately
	if active_player == 2:
		call_deferred("ai_play_turn")

func _on_hand_updated() -> void:
	var current_state = hand_manager.get_current_hand_state()
	ui_manager.update_ui(current_state)
	effect_manager.update_effects()

func _on_game_state_updated(state: Dictionary) -> void:
	hand_manager.apply_game_state(state)
	ui_manager.update_ui(hand_manager.get_current_hand_state())

func _input(event: InputEvent) -> void:
	# Detect right click anywhere in the scene
	if event is InputEventMouseButton and event.button_index == 2 and event.pressed: # 2 = right mouse button
		# Deselect all cards
		hand_manager.clear_selection()

	# Debug key (D key) to print game state
	if event.is_action_pressed("ui_accept"):  # Enter key for debug
		debug_game_state()

func _on_card_selected(card_id: int) -> void:
	hand_manager.select_card(card_id)
	effect_manager.play_card_selection_effect(card_id)

# Button handlers
func _on_menu_button_pressed() -> void:
	# Return to main menu
	get_tree().change_scene_to_file("res://Scenes/MainMenu.tscn")

# Find and select the best possible hand
func _on_best_hand_button_pressed() -> void:
	# Clear current selection
	hand_manager.clear_selection()
	# Find and select the best hand
	hand_manager.select_best_hand()

# Open the rank change panel
func _on_cheat_rank_button_pressed() -> void:
	print("Cheat rank button pressed")

	# Check if it's the player's turn
	if (active_player == 1 and player1_action_taken) or (active_player == 2 and player2_action_taken):
		print("It's not your turn or you've already performed an action")
		return

	# Check if opponent has played (can no longer cheat)
	if (active_player == 1 and player2_has_played) or (active_player == 2 and player1_has_played):
		print("Opponent has already played, you must play too")
		return

	if cheat_rank_used:
		print("Cheat already used this turn")
		return # Can only use once per turn

	# Get the currently selected card
	var selected_cards = hand_manager.get_selected_cards()
	print("Selected cards: ", selected_cards)
	if selected_cards.size() != 1:
		print("Must select exactly one card")
		return

	selected_card_for_cheat = selected_cards[0]
	print("Selected card for cheat: ", selected_card_for_cheat)

	# Créer une visualisation des options de rang
	create_rank_options_visualization(selected_card_for_cheat)

	change_rank_panel.visible = true
	change_color_panel.visible = false

	# Mark that player has performed an action
	if active_player == 1:
		player1_action_taken = true
	else:
		player2_action_taken = true

	# Switch to next player
	active_player = 3 - active_player  # 1 -> 2, 2 -> 1

	# Update interface
	update_game_ui()

# Open the color change panel
func _on_cheat_color_button_pressed() -> void:
	# Vérifier si c'est le tour du joueur
	if (active_player == 1 and player1_action_taken) or (active_player == 2 and player2_action_taken):
		print("Ce n'est pas votre tour ou vous avez déjà effectué une action")
		return

	# Vérifier si l'adversaire a joué (on ne peut plus tricher)
	if (active_player == 1 and player2_has_played) or (active_player == 2 and player1_has_played):
		print("L'adversaire a déjà joué, vous devez jouer aussi")
		return

	if cheat_color_used:
		return # Can only use once per turn

	# Get the currently selected card
	var selected_cards = hand_manager.get_selected_cards()
	if selected_cards.size() != 1:
		# Must select exactly one card
		return

	selected_card_for_cheat = selected_cards[0]

	# Créer une visualisation des options de couleur
	create_suit_options_visualization(selected_card_for_cheat)

	change_color_panel.visible = true
	change_rank_panel.visible = false

	# Marquer que le joueur a effectué une action
	if active_player == 1:
		player1_action_taken = true
	else:
		player2_action_taken = true

	# Passer au joueur suivant
	active_player = 3 - active_player  # 1 -> 2, 2 -> 1

	# Mettre à jour l'interface
	update_game_ui()

# Validate the current hand (final action)
func _on_validate_hand_button_pressed() -> void:
	var selected_cards = hand_manager.get_selected_cards()
	if selected_cards.size() == 0:
		print("Must select at least one card to validate")
		return

	# Check if player can validate
	if (active_player == 1 and player1_has_played) or (active_player == 2 and player2_has_played):
		print("This player has already validated their hand")
		return

	print("Player ", active_player, " is validating their hand")

	# If opponent has played, we must play too
	if active_player == 1 and player2_has_played and player1_action_taken and not player1_has_played:
		# Player 1 must play
		play_hand(1, selected_cards)
		# Deselect cards
		hand_manager.clear_selection()
		# Check if round is over
		if player1_has_played and player2_has_played:
			end_round()
		return

	if active_player == 2 and player1_has_played and player2_action_taken and not player2_has_played:
		# Le joueur 2 doit jouer
		play_hand(2, selected_cards)
		# Désélectionner les cartes
		hand_manager.clear_selection()
		# Vérifier si le round est terminé
		if player1_has_played and player2_has_played:
			end_round()
		return

	# Jouer normalement
	if active_player == 1 and not player1_action_taken:
		play_hand(1, selected_cards)
		player1_action_taken = true
		# Désélectionner les cartes
		hand_manager.clear_selection()
		# Passer au joueur suivant
		active_player = 2
	elif active_player == 2 and not player2_action_taken:
		play_hand(2, selected_cards)
		player2_action_taken = true
		# Désélectionner les cartes
		hand_manager.clear_selection()
		# Passer au joueur suivant
		active_player = 1

	# Mettre à jour l'interface
	update_game_ui()

# Crée une visualisation des options de rang pour une carte
func create_rank_options_visualization(card_id: int) -> void:
	print("Creating rank options visualization for card ID: ", card_id)
	# Nettoyer le panel existant
	for child in change_rank_panel.get_children():
		if child is Label:
			continue # Garder seulement le label
		child.queue_free()

	# Obtenir les informations de la carte
	var card_info = hand_manager.get_card_info(card_id)
	print("Card info: ", card_info)
	if not card_info:
		print("No card info found")
		return

	# Créer un conteneur principal
	var main_container = VBoxContainer.new()
	main_container.alignment = BoxContainer.ALIGNMENT_CENTER
	main_container.size_flags_horizontal = Control.SIZE_FILL
	main_container.size_flags_vertical = Control.SIZE_FILL
	main_container.position.y = 60
	change_rank_panel.add_child(main_container)

	# Créer un conteneur pour les options
	var options_container = HBoxContainer.new()
	options_container.alignment = BoxContainer.ALIGNMENT_CENTER
	options_container.size_flags_horizontal = Control.SIZE_FILL
	options_container.size_flags_vertical = Control.SIZE_FILL
	main_container.add_child(options_container)

	# Créer les options de carte (seulement rang-1 et rang+1, pas le rang actuel)
	var ranks = []
	if card_info.value > 1:
		ranks.append(card_info.value - 1) # Rang -1
	if card_info.value < 13:
		ranks.append(card_info.value + 1) # Rang +1

	for rank in ranks:
		var card_button = create_card_option(card_info.id, rank, card_info.suit)
		# Connecter le signal pour changer le rang
		print("Connecting signal for rank: ", rank)
		card_button.pressed.connect(func(): _on_rank_option_selected(card_id, rank))
		options_container.add_child(card_button)

# Crée une visualisation des options de couleur pour une carte
func create_suit_options_visualization(card_id: int) -> void:
	# Nettoyer le panel existant
	for child in change_color_panel.get_children():
		if child is Label:
			continue # Garder seulement le label
		child.queue_free()

	# Obtenir les informations de la carte
	var card_info = hand_manager.get_card_info(card_id)
	if not card_info:
		return

	# Créer un conteneur principal
	var main_container = VBoxContainer.new()
	main_container.alignment = BoxContainer.ALIGNMENT_CENTER
	main_container.size_flags_horizontal = Control.SIZE_FILL
	main_container.size_flags_vertical = Control.SIZE_FILL
	main_container.position.y = 60
	change_color_panel.add_child(main_container)

	# Créer un conteneur pour les options
	var options_container = HBoxContainer.new()
	options_container.alignment = BoxContainer.ALIGNMENT_CENTER
	options_container.size_flags_horizontal = Control.SIZE_FILL
	options_container.size_flags_vertical = Control.SIZE_FILL
	main_container.add_child(options_container)

	# Créer les options de couleur (sauf la couleur actuelle)
	var suits = ["hearts", "diamonds", "clubs", "spades"]

	for suit in suits:
		# Ne pas inclure la couleur actuelle dans les options
		if suit == card_info.suit:
			continue

		var card_button = create_card_option(card_info.id, card_info.value, suit)
		# Connecter le signal pour changer la couleur
		print("Connecting signal for suit: ", suit)
		card_button.pressed.connect(func(): _on_suit_option_selected(card_id, suit))
		options_container.add_child(card_button)

# Crée un bouton de carte pour les options de modification
func create_card_option(_card_id: int, rank: int, suit: String) -> TextureButton:
	var button = TextureButton.new()
	button.custom_minimum_size = Vector2(100, 150) # Taille des cartes normales pour meilleure visibilité

	# Chargement de la texture en fonction de la couleur de la carte
	var texture_path = "res://Assets/cards/Top-Down/Cards/"
	match suit.to_lower():
		"hearts":
			texture_path += "Hearts.png"
		"diamonds":
			texture_path += "Diamonds.png"
		"clubs":
			texture_path += "Clubs.png"
		"spades":
			texture_path += "Spades.png"

	var spritesheet = load(texture_path)
	var atlas = AtlasTexture.new()
	atlas.atlas = spritesheet

	# Calcul de la position de la carte dans le spritesheet
	var rank_index = rank - 1  # -1 car les rangs commencent à 1
	var x = (rank_index % 5) * (86 + 2)  # 5 cartes par ligne, 86px de large + 2px d'espacement
	var y = (rank_index / 5) * (122 + 2) # 122px de haut + 2px d'espacement

	atlas.region = Rect2(x, y, 86, 122)
	button.texture_normal = atlas

	# Ajouter un effet de survol
	button.mouse_entered.connect(func():
		button.scale = Vector2(1.15, 1.15)
		button.modulate = Color(1.0, 0.9, 0.2) # Teinte dorée au survol
	)
	button.mouse_exited.connect(func():
		button.scale = Vector2(1.0, 1.0)
		button.modulate = Color(1.0, 1.0, 1.0)
	)

	# Ajouter une ombre pour effet 3D
	var shadow = ColorRect.new()
	shadow.color = Color(0, 0, 0, 0.3)
	shadow.size = button.custom_minimum_size
	shadow.position = Vector2(4, 4)
	shadow.mouse_filter = Control.MOUSE_FILTER_IGNORE
	button.add_child(shadow)
	shadow.z_index = -1

	return button

# Gestionnaire pour la sélection d'une option de rang
func _on_rank_option_selected(card_id: int, new_rank: int) -> void:
	print("Rank option selected: ", card_id, " new rank: ", new_rank)
	if selected_card_for_cheat == null:
		print("No card selected for cheat")
		return

	# Obtenir le rang actuel
	var card_info = hand_manager.get_card_info(card_id)
	print("Card info for modification: ", card_info)
	if not card_info:
		print("No card info found for modification")
		return

	# Calculer le changement de rang
	var change = new_rank - card_info.value
	print("Changing rank by: ", change)

	# Modifier le rang de la carte
	hand_manager.modify_card_rank(card_id, change)

	# Cacher le panel et marquer la triche comme utilisée
	change_rank_panel.visible = false
	cheat_rank_used = true
	cheat_rank_button.disabled = true
	selected_card_for_cheat = null

# Gestionnaire pour la sélection d'une option de couleur
func _on_suit_option_selected(card_id: int, new_suit: String) -> void:
	print("Suit option selected: ", card_id, " new suit: ", new_suit)
	if selected_card_for_cheat == null:
		print("No card selected for suit change")
		return

	# Modifier la couleur de la carte
	print("Modifying card suit to: ", new_suit)
	hand_manager.modify_card_suit(card_id, new_suit)

	# Cacher le panel et marquer la triche comme utilisée
	change_color_panel.visible = false
	cheat_color_used = true
	cheat_color_button.disabled = true
	selected_card_for_cheat = null

# Les anciennes fonctions de gestion des boutons ont été supprimées
# Nous utilisons maintenant _on_rank_option_selected et _on_suit_option_selected

# Détermine le premier joueur aléatoirement (pile ou face)
func determine_first_player() -> void:
	# Coin flip (50% chance for each player)
	if randf() < 0.5:
		active_player = 1
		print("Player 1 starts")
	else:
		active_player = 2
		print("Player 2 starts")

# Debug function to print game state
func debug_game_state() -> void:
	print("=== GAME DEBUG ===")
	print("Active player: ", active_player)
	print("Player 1 hand size: ", hand_manager.player1_hand.size())
	print("Player 2 hand size: ", hand_manager.player2_hand.size())
	print("Player 1 action taken: ", player1_action_taken)
	print("Player 2 action taken: ", player2_action_taken)
	print("Player 1 has played: ", player1_has_played)
	print("Player 2 has played: ", player2_has_played)
	print("Selected cards: ", hand_manager.get_selected_cards().size())
	print("===================")

# Met à jour l'interface utilisateur en fonction de l'état du jeu
func update_game_ui() -> void:
	# Mettre à jour le texte indiquant le joueur actif
	var hand_value_label = get_node("HandValue")
	if game_state == "playing":
		hand_value_label.text = "Tour du Joueur " + str(active_player) + " - Round " + str(current_round) + "/" + str(max_rounds) + " - Score: " + str(player1_score) + "-" + str(player2_score)

	# Activer/désactiver les boutons en fonction du joueur actif et des actions disponibles
	if active_player == 1:
		# Activer les boutons pour le joueur 1 seulement si c'est son tour
		cheat_rank_button.disabled = player1_action_taken or player2_has_played or cheat_rank_used
		cheat_color_button.disabled = player1_action_taken or player2_has_played or cheat_color_used
		validate_hand_button.disabled = player1_has_played

		# Ajouter un indicateur visuel pour le tour du joueur 1
		hand_value_label.add_theme_color_override("font_color", Color(1, 1, 1, 1))
	else:
		# Joueur 2 (IA) - désactiver tous les boutons
		cheat_rank_button.disabled = true
		cheat_color_button.disabled = true
		validate_hand_button.disabled = true

		# Ajouter un indicateur visuel pour le tour de l'IA
		hand_value_label.add_theme_color_override("font_color", Color(1, 0.5, 0, 1)) # Orange pour l'IA

		# Faire jouer l'IA après un court délai
		if not player2_action_taken and not player2_has_played:
			# Créer un timer pour simuler la réflexion de l'IA
			var timer = get_tree().create_timer(1.5)
			timer.timeout.connect(func(): ai_play_turn())

# Play the specified player's hand
func play_hand(player: int, selected_cards: Array) -> void:
	# Record played cards
	if player == 1:
		player1_played_hand = selected_cards.duplicate()
		player1_has_played = true
	else:
		player2_played_hand = selected_cards.duplicate()
		player2_has_played = true

	# Evaluate the specific hand played
	var hand_value = ""
	if player == 1:
		# For player 1, use the selected cards from UI
		hand_value = hand_manager.evaluate_selected_hand()
	else:
		# For player 2 (AI), evaluate the specific cards passed
		var cards_info = hand_manager.get_cards_info(selected_cards)
		hand_value = hand_manager.evaluate_hand(cards_info)

	print("Player ", player, " played: ", hand_value)

	# Display temporary message
	var hand_value_label = get_node("HandValue")
	hand_value_label.text = "Player " + str(player) + " played: " + hand_value

# Termine le round actuel et détermine le gagnant
func end_round() -> void:
	game_state = "round_end"

	# Récupérer les informations sur les mains jouées
	var player1_hand_info = hand_manager.get_cards_info(player1_played_hand)
	var player2_hand_info = hand_manager.get_cards_info(player2_played_hand)

	# Vérifier si des cartes ont été modifiées
	var player1_has_modified_cards = has_modified_cards(player1_hand_info)
	var player2_has_modified_cards = has_modified_cards(player2_hand_info)

	# Évaluer les mains
	var player1_hand_value = hand_manager.evaluate_hand(player1_hand_info)
	var player2_hand_value = hand_manager.evaluate_hand(player2_hand_info)
	var player1_hand_score = hand_manager.get_hand_score(player1_hand_value)
	var player2_hand_score = hand_manager.get_hand_score(player2_hand_value)

	# Déterminer le gagnant selon les règles
	var winner = 0
	var result_text = ""

	# Règle spéciale: si un seul joueur a triché, l'autre gagne
	if player1_has_modified_cards and not player2_has_modified_cards:
		winner = 2
		result_text = "Le joueur 2 gagne car le joueur 1 a triché !"
	elif player2_has_modified_cards and not player1_has_modified_cards:
		winner = 1
		result_text = "Le joueur 1 gagne car le joueur 2 a triché !"
	else:
		# Les deux ont triché ou aucun n'a triché, on compare les mains
		if player1_hand_score > player2_hand_score:
			winner = 1
			result_text = "Le joueur 1 gagne avec " + player1_hand_value + " contre " + player2_hand_value
		elif player2_hand_score > player1_hand_score:
			winner = 2
			result_text = "Le joueur 2 gagne avec " + player2_hand_value + " contre " + player1_hand_value
		else:
			# Égalité, comparer la carte la plus haute
			result_text = "Égalité ! " + player1_hand_value

	# Mettre à jour le score
	if winner == 1:
		player1_score += 1
	elif winner == 2:
		player2_score += 1

	# Afficher le résultat
	winner_screen.visible = true
	winner_screen.get_node("Label").text = result_text + "\n\nScore: " + str(player1_score) + "-" + str(player2_score)

	# Préparer le prochain round
	current_round += 1
	if current_round > max_rounds or player1_score > max_rounds/2 or player2_score > max_rounds/2:
		# Fin de la partie
		game_state = "game_end"
		var final_result = ""
		if player1_score > player2_score:
			final_result = "Le joueur 1 remporte la partie !"
		elif player2_score > player1_score:
			final_result = "Le joueur 2 remporte la partie !"
		else:
			final_result = "Égalité !"
		winner_screen.get_node("Label").text += "\n\n" + final_result
		# Ajouter un bouton pour recommencer
		var restart_button = Button.new()
		restart_button.text = "Nouvelle partie"
		restart_button.position = Vector2(350, 500)
		restart_button.size = Vector2(200, 50)
		restart_button.pressed.connect(func(): get_tree().reload_current_scene())
		winner_screen.add_child(restart_button)
	else:
		# Préparer le prochain round
		var continue_button = Button.new()
		continue_button.text = "Round suivant"
		continue_button.position = Vector2(350, 500)
		continue_button.size = Vector2(200, 50)
		continue_button.pressed.connect(start_next_round)
		winner_screen.add_child(continue_button)

# Vérifie si une main contient des cartes modifiées
func has_modified_cards(cards_info: Array) -> bool:
	for card in cards_info:
		if card.has("modified") and card.modified:
			return true
	return false

# Fonction pour faire jouer l'IA (joueur 2)
func ai_play_turn() -> void:
	if player2_action_taken or player2_has_played or game_state != "playing":
		# If AI has already played, ensure player 1 buttons are correctly enabled
		if active_player == 1:
			update_game_ui()
		return

	# Disable all player 1 interaction during AI turn
	cheat_rank_button.disabled = true
	cheat_color_button.disabled = true
	validate_hand_button.disabled = true

	# Display message indicating AI is thinking
	var hand_value_label = get_node("HandValue")
	hand_value_label.text = "AI is thinking..."

	# Decide if AI will cheat or play directly
	# 40% chance to cheat if powers are available
	var can_cheat_rank = not cheat_rank_used
	var can_cheat_color = not cheat_color_used
	var will_cheat = randf() < 0.4 and (can_cheat_rank or can_cheat_color)

	if will_cheat:
		# AI decides to cheat
		print("AI decides to cheat")

		# Sélectionner une carte au hasard dans la main du joueur 2
		var player2_cards = hand_manager.player2_hand
		if player2_cards.size() > 0:
			var random_card_index = randi() % player2_cards.size()
			var card_to_modify = player2_cards[random_card_index]
			var cheat_success = false

			# Décider quel type de triche utiliser en fonction des options disponibles
			if can_cheat_rank and can_cheat_color:
				# Les deux options sont disponibles, choisir aléatoirement
				var cheat_type = randi() % 2  # 0 = rang, 1 = couleur
				if cheat_type == 0:
					modify_card_rank_ai(card_to_modify)
				else:
					modify_card_suit_ai(card_to_modify)
				cheat_success = true
			elif can_cheat_rank:
				# Seulement la modification de rang est disponible
				cheat_success = modify_card_rank_ai(card_to_modify)
			elif can_cheat_color:
				# Seulement la modification de couleur est disponible
				cheat_success = modify_card_suit_ai(card_to_modify)

			if cheat_success:
				# Marquer que l'IA a effectué une action
				player2_action_taken = true

				# Passer au joueur 1
				active_player = 1
				update_game_ui()

				# Display message
				hand_value_label.text = "Player 2 modified a card. Player 1's turn"
				return

	# If cheat failed or wasn't chosen, play directly
	# AI decides to play directly
	print("AI decides to play")

	# Select best cards to form a hand
	var best_hand = ai_select_best_hand()
	if best_hand.size() > 0:
		# Play the hand
		play_hand(2, best_hand)

		# If player 1 has already played, end the round
		if player1_has_played:
			end_round()
		else:
			# Switch to player 1
			active_player = 1
			update_game_ui()

			# Re-enable player 1 buttons
			cheat_rank_button.disabled = player1_action_taken or cheat_rank_used
			cheat_color_button.disabled = player1_action_taken or cheat_color_used
			validate_hand_button.disabled = player1_has_played

		# Display message
		hand_value_label.text = "Player 2 played their hand. Player 1's turn"
	else:
		print("AI Error: Could not select any cards")
		hand_value_label.text = "AI Error - Player 1's turn"

# Sélectionne la meilleure main possible pour l'IA
func ai_select_best_hand() -> Array:
	# Use algorithm to select best hand
	var best_hand_ids = []
	var best_hand_score = -1
	var best_hand_cards = []

	# Create list of all player 2 cards
	var card_objects = []
	for card in hand_manager.player2_hand:
		card_objects.append({
			"id": card.id,
			"rank": card.value,
			"suit": card.suit,
			"modified": card.get("modified", false)
		})

	# If no cards available, return empty
	if card_objects.is_empty():
		print("AI Error: No cards available for player 2")
		return []

	# Check all possible combinations
	var combinations = hand_manager.get_all_combinations(card_objects)

	# If no combinations found, use single best card
	if combinations.is_empty():
		print("AI Warning: No combinations found, selecting single best card")
		var best_card = card_objects[0]
		for card in card_objects:
			if card.rank > best_card.rank:
				best_card = card
		return [best_card.id]

	for combo in combinations:
		# Convert to format expected by evaluator
		var eval_cards = []
		for card in combo:
			eval_cards.append({
				"rank": card.rank,
				"suit": card.suit
			})

		# Evaluate this combination
		var hand_value = hand_manager.poker_hand_evaluator.evaluate_hand(eval_cards)
		var hand_score = hand_manager.get_hand_score(hand_value)

		# If this is better than our current best hand, update
		if hand_score > best_hand_score:
			best_hand_score = hand_score
			best_hand_cards = combo

	# Extract IDs of cards from the best hand
	for card in best_hand_cards:
		best_hand_ids.append(card.id)

	# Fallback: if no best hand found, select first card
	if best_hand_ids.is_empty() and not card_objects.is_empty():
		print("AI Warning: No best hand found, selecting fallback card")
		best_hand_ids.append(card_objects[0].id)

	print("AI selected ", best_hand_ids.size(), " cards: ", best_hand_ids)
	return best_hand_ids

# Function to modify card rank by AI
func modify_card_rank_ai(card: Dictionary) -> bool:
	# Modifier le rang
	var change = [-1, 1][randi() % 2]  # -1 ou +1
	var new_rank = clamp(card.value + change, 1, 13)
	if new_rank != card.value:
		print("L'IA modifie le rang de la carte ", card.id, " de ", card.value, " à ", new_rank)
		card.value = new_rank
		card.modified = true
		cheat_rank_used = true
		hand_manager.emit_signal("hand_updated")
		return true
	return false

# Fonction pour modifier la couleur d'une carte par l'IA
func modify_card_suit_ai(card: Dictionary) -> bool:
	# Modifier la couleur
	var suits = ["hearts", "diamonds", "clubs", "spades"]
	suits.erase(card.suit)
	var new_suit = suits[randi() % suits.size()]
	print("L'IA modifie la couleur de la carte ", card.id, " de ", card.suit, " à ", new_suit)
	card.suit = new_suit
	card.modified = true
	cheat_color_used = true
	hand_manager.emit_signal("hand_updated")
	return true

# Démarre le round suivant
func start_next_round() -> void:
	# Réinitialiser les variables
	player1_action_taken = false
	player2_action_taken = false
	player1_has_played = false
	player2_has_played = false
	player1_played_hand = []
	player2_played_hand = []
	cheat_rank_used = false
	cheat_color_used = false

	# Réactiver les boutons de triche
	cheat_rank_button.disabled = false
	cheat_color_button.disabled = false
	validate_hand_button.disabled = false

	# Cacher l'écran de résultat
	winner_screen.visible = false

	# Supprimer les boutons ajoutés
	for child in winner_screen.get_children():
		if child is Button:
			child.queue_free()

	# Désélectionner toutes les cartes
	hand_manager.clear_selection()

	# Déterminer le premier joueur pour ce round
	determine_first_player()

	# Distribuer de nouvelles cartes
	hand_manager.initialize_solo_game()

	# Mettre à jour l'interface
	game_state = "playing"
	update_game_ui()
