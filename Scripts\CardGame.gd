extends Control
class_name CardGame

# UI References
@onready var main_container: VBoxContainer = $MainContainer
@onready var phase_label: Label = $MainContainer/PhaseLabel
@onready var round_score_label: Label = $MainContainer/RoundScoreLabel
@onready var instruction_label: Label = $MainContainer/InstructionLabel
@onready var hand_container: HBoxContainer = $MainContainer/HandContainer
@onready var button_container: HBoxContainer = $MainContainer/ButtonContainer

# Buttons
var proceed_button: Button
var exchange_button: Button
var modify_rank_button: Button
var modify_suit_button: Button
var validate_button: Button

# Game state
enum Phase { SELECTION, MODIFICATION, VALIDATION, REVEAL, ROUND_END }
var current_phase: Phase = Phase.SELECTION
var current_round: int = 1
var player1_score: int = 0
var player2_score: int = 0

# Round state
var player1_selected_cards: Array = []
var player2_selected_cards: Array = []
var player1_has_proceeded: bool = false
var player2_has_proceeded: bool = false
var player1_has_modified: bool = false
var player2_has_modified: bool = false
var player1_modification_type: String = ""  # "", "exchange", "illegal"
var player2_modification_type: String = ""
var player1_validated: bool = false
var player2_validated: bool = false

# Hands
var player1_hand: Array = []
var player2_hand: Array = []

# UI state
var selected_card_ids: Array = []

func _ready():
	create_ui()
	start_new_game()

func create_ui():
	# Create buttons
	proceed_button = Button.new()
	proceed_button.text = "Proceed"
	proceed_button.pressed.connect(_on_proceed_pressed)
	button_container.add_child(proceed_button)
	
	exchange_button = Button.new()
	exchange_button.text = "Exchange Card"
	exchange_button.pressed.connect(_on_exchange_pressed)
	button_container.add_child(exchange_button)
	
	modify_rank_button = Button.new()
	modify_rank_button.text = "Modify Rank (Cheat)"
	modify_rank_button.pressed.connect(_on_modify_rank_pressed)
	button_container.add_child(modify_rank_button)
	
	modify_suit_button = Button.new()
	modify_suit_button.text = "Modify Suit (Cheat)"
	modify_suit_button.pressed.connect(_on_modify_suit_pressed)
	button_container.add_child(modify_suit_button)
	
	validate_button = Button.new()
	validate_button.text = "Validate Hand"
	validate_button.pressed.connect(_on_validate_pressed)
	button_container.add_child(validate_button)

func start_new_game():
	current_round = 1
	player1_score = 0
	player2_score = 0
	start_round()

func start_round():
	print("=== ROUND ", current_round, " ===")
	reset_round_state()
	generate_hands()
	current_phase = Phase.SELECTION
	update_ui()

func reset_round_state():
	player1_selected_cards.clear()
	player2_selected_cards.clear()
	player1_has_proceeded = false
	player2_has_proceeded = false
	player1_has_modified = false
	player2_has_modified = false
	player1_modification_type = ""
	player2_modification_type = ""
	player1_validated = false
	player2_validated = false
	selected_card_ids.clear()

func generate_hands():
	# Create a deck and shuffle
	var deck = CardUtils.create_full_deck()
	deck.shuffle()
	
	# Deal 8 cards to each player
	player1_hand.clear()
	player2_hand.clear()
	
	for i in range(8):
		player1_hand.append(deck[i])
		player2_hand.append(deck[i + 8])
	
	print("Player 1 hand: ", player1_hand.size(), " cards")
	print("Player 2 hand: ", player2_hand.size(), " cards")

func update_ui():
	# Update labels
	phase_label.text = get_phase_name()
	round_score_label.text = "Round " + str(current_round) + "/3 | Score: " + str(player1_score) + " - " + str(player2_score)
	instruction_label.text = get_phase_instructions()
	
	# Update buttons
	update_button_visibility()
	
	# Update hand display
	update_hand_display()

func get_phase_name() -> String:
	match current_phase:
		Phase.SELECTION: return "CARD SELECTION"
		Phase.MODIFICATION: return "MODIFICATION"
		Phase.VALIDATION: return "VALIDATION"
		Phase.REVEAL: return "REVEAL"
		Phase.ROUND_END: return "ROUND END"
		_: return "UNKNOWN"

func get_phase_instructions() -> String:
	match current_phase:
		Phase.SELECTION: 
			return "Select 1-5 cards from your hand (" + str(selected_card_ids.size()) + "/5 selected)"
		Phase.MODIFICATION: 
			if player1_has_modified:
				return "Modification used. Click Validate to finish."
			else:
				return "Exchange a card (legal) or modify a card (illegal), or validate directly"
		Phase.VALIDATION: 
			return "Waiting for opponent to validate..."
		Phase.REVEAL: 
			return "Revealing hands..."
		Phase.ROUND_END: 
			return "Round completed"
		_: 
			return ""

func update_button_visibility():
	# Hide all buttons first
	proceed_button.visible = false
	exchange_button.visible = false
	modify_rank_button.visible = false
	modify_suit_button.visible = false
	validate_button.visible = false
	
	match current_phase:
		Phase.SELECTION:
			proceed_button.visible = true
			proceed_button.disabled = selected_card_ids.is_empty()
		
		Phase.MODIFICATION:
			exchange_button.visible = true
			modify_rank_button.visible = true
			modify_suit_button.visible = true
			validate_button.visible = true
			
			# Disable if already modified
			exchange_button.disabled = player1_has_modified
			modify_rank_button.disabled = player1_has_modified
			modify_suit_button.disabled = player1_has_modified

func update_hand_display():
	# Clear existing cards
	for child in hand_container.get_children():
		child.queue_free()
	
	# Display player 1 hand
	for card in player1_hand:
		var card_button = create_card_button(card)
		hand_container.add_child(card_button)

func create_card_button(card: Dictionary) -> Button:
	var button = Button.new()
	button.text = get_card_text(card)
	button.custom_minimum_size = Vector2(60, 80)
	
	# Highlight if selected
	if selected_card_ids.has(card.id):
		button.modulate = Color.YELLOW
	
	# Connect click
	button.pressed.connect(func(): on_card_clicked(card.id))
	
	return button

func get_card_text(card: Dictionary) -> String:
	var rank = card.get("value", 1)
	var suit = card.get("suit", "hearts")
	
	var rank_text = ""
	match rank:
		1: rank_text = "A"
		11: rank_text = "J"
		12: rank_text = "Q"
		13: rank_text = "K"
		_: rank_text = str(rank)
	
	var suit_symbol = ""
	match suit:
		"hearts": suit_symbol = "♥"
		"diamonds": suit_symbol = "♦"
		"clubs": suit_symbol = "♣"
		"spades": suit_symbol = "♠"
	
	return rank_text + suit_symbol

func on_card_clicked(card_id: int):
	if current_phase != Phase.SELECTION:
		return
	
	if selected_card_ids.has(card_id):
		selected_card_ids.erase(card_id)
	else:
		if selected_card_ids.size() < 5:
			selected_card_ids.append(card_id)
		else:
			show_message("Maximum 5 cards allowed")
			return
	
	update_ui()

# Button handlers
func _on_proceed_pressed():
	if selected_card_ids.is_empty():
		show_message("Select at least one card")
		return
	
	player1_selected_cards = selected_card_ids.duplicate()
	player1_has_proceeded = true
	
	# AI makes its selection
	ai_make_selection()
	
	# Both proceeded, advance to modification
	current_phase = Phase.MODIFICATION
	update_ui()

func _on_exchange_pressed():
	if selected_card_ids.size() != 1:
		show_message("Select exactly 1 card to exchange")
		return
	
	# Simple exchange: replace with a random card from hand
	var selected_id = selected_card_ids[0]
	var available_cards = []
	
	# Find cards not selected
	for card in player1_hand:
		if not selected_card_ids.has(card.id):
			available_cards.append(card)
	
	if available_cards.is_empty():
		show_message("No cards available for exchange")
		return
	
	# Pick random card to exchange with
	var new_card = available_cards[randi() % available_cards.size()]
	
	# Replace in selection
	var index = selected_card_ids.find(selected_id)
	selected_card_ids[index] = new_card.id
	
	# Update player selection
	player1_selected_cards = selected_card_ids.duplicate()
	player1_has_modified = true
	player1_modification_type = "exchange"
	
	show_message("Card exchanged (LEGAL)")
	update_ui()

func _on_modify_rank_pressed():
	if selected_card_ids.size() != 1:
		show_message("Select exactly 1 card to modify")
		return

	# Simple modification: increase rank by 1
	var card_id = selected_card_ids[0]
	modify_card_rank(card_id, 1)
	player1_has_modified = true
	player1_modification_type = "illegal"
	show_message("Card rank modified (ILLEGAL)")
	update_ui()

func _on_modify_suit_pressed():
	if selected_card_ids.size() != 1:
		show_message("Select exactly 1 card to modify")
		return

	# Simple modification: change to hearts
	var card_id = selected_card_ids[0]
	modify_card_suit(card_id, "hearts")
	player1_has_modified = true
	player1_modification_type = "illegal"
	show_message("Card suit modified (ILLEGAL)")
	update_ui()

func _on_validate_pressed():
	player1_validated = true

	# AI validates
	ai_validate()

	# Both validated, reveal
	current_phase = Phase.REVEAL
	show_round_results()

func modify_card_rank(card_id: int, rank_change: int):
	for card in player1_hand:
		if card.id == card_id:
			card.value = clamp(card.value + rank_change, 1, 13)
			card.modified = true
			break

func modify_card_suit(card_id: int, new_suit: String):
	for card in player1_hand:
		if card.id == card_id:
			card.suit = new_suit
			card.modified = true
			break

# AI Logic
func ai_make_selection():
	# AI selects 2-4 cards randomly
	var num_cards = randi_range(2, 4)
	player2_selected_cards.clear()

	var available_cards = player2_hand.duplicate()
	available_cards.shuffle()

	for i in range(min(num_cards, available_cards.size())):
		player2_selected_cards.append(available_cards[i].id)

	player2_has_proceeded = true
	print("AI selected ", player2_selected_cards.size(), " cards")

func ai_validate():
	# AI decides whether to cheat (30% chance)
	if randf() < 0.3 and not player2_has_modified:
		# AI cheats
		if player2_selected_cards.size() > 0:
			var card_id = player2_selected_cards[0]
			if randf() < 0.5:
				modify_ai_card_rank(card_id, randi_range(-2, 3))
				player2_modification_type = "illegal"
			else:
				modify_ai_card_suit(card_id, ["hearts", "diamonds", "clubs", "spades"][randi() % 4])
				player2_modification_type = "illegal"
			player2_has_modified = true
			print("AI cheated")

	player2_validated = true
	print("AI validated")

func modify_ai_card_rank(card_id: int, rank_change: int):
	for card in player2_hand:
		if card.id == card_id:
			card.value = clamp(card.value + rank_change, 1, 13)
			card.modified = true
			break

func modify_ai_card_suit(card_id: int, new_suit: String):
	for card in player2_hand:
		if card.id == card_id:
			card.suit = new_suit
			card.modified = true
			break

func show_round_results():
	current_phase = Phase.REVEAL

	# Get selected cards info
	var p1_cards = get_cards_by_ids(player1_hand, player1_selected_cards)
	var p2_cards = get_cards_by_ids(player2_hand, player2_selected_cards)

	# Evaluate hands
	var p1_hand_value = evaluate_hand(p1_cards)
	var p2_hand_value = evaluate_hand(p2_cards)
	var p1_score = get_hand_score(p1_hand_value)
	var p2_score = get_hand_score(p2_hand_value)

	# Determine winner
	var winner = 0
	var reason = ""

	var p1_cheated = player1_modification_type == "illegal"
	var p2_cheated = player2_modification_type == "illegal"

	if p1_cheated and not p2_cheated:
		winner = 2
		reason = "Player 1 cheated - Player 2 wins!"
	elif p2_cheated and not p1_cheated:
		winner = 1
		reason = "Player 2 cheated - Player 1 wins!"
	elif p1_cheated and p2_cheated:
		if p1_score > p2_score:
			winner = 1
			reason = "Both cheated - Player 1 wins with " + p1_hand_value
		elif p2_score > p1_score:
			winner = 2
			reason = "Both cheated - Player 2 wins with " + p2_hand_value
		else:
			winner = 0
			reason = "Both cheated - Tie"
	else:
		if p1_score > p2_score:
			winner = 1
			reason = "Player 1 wins: " + p1_hand_value + " vs " + p2_hand_value
		elif p2_score > p1_score:
			winner = 2
			reason = "Player 2 wins: " + p2_hand_value + " vs " + p1_hand_value
		else:
			winner = 0
			reason = "Tie: " + p1_hand_value

	# Update score
	if winner == 1:
		player1_score += 1
	elif winner == 2:
		player2_score += 1

	# Show results
	show_victory_screen(winner, reason, p1_cards, p2_cards, p1_hand_value, p2_hand_value)

func get_cards_by_ids(hand: Array, card_ids: Array) -> Array:
	var cards = []
	for card_id in card_ids:
		for card in hand:
			if card.id == card_id:
				cards.append(card)
				break
	return cards

func evaluate_hand(cards: Array) -> String:
	if cards.is_empty():
		return "No cards"

	var poker_evaluator = load("res://Scripts/PokerHand.gd").new()
	var eval_cards = CardUtils.convert_cards_for_evaluation(cards)
	return poker_evaluator.evaluate_hand(eval_cards)

func get_hand_score(hand_value: String) -> int:
	if hand_value.begins_with("Straight Flush"):
		return 9
	elif hand_value.begins_with("Four of a Kind"):
		return 8
	elif hand_value.begins_with("Full House"):
		return 7
	elif hand_value.begins_with("Flush"):
		return 6
	elif hand_value.begins_with("Straight"):
		return 5
	elif hand_value.begins_with("Three of a Kind"):
		return 4
	elif hand_value.begins_with("Two Pair"):
		return 3
	elif hand_value.begins_with("Pair"):
		return 2
	elif hand_value.begins_with("High Card"):
		return 1
	else:
		return 0

func show_victory_screen(_winner: int, reason: String, p1_cards: Array, p2_cards: Array, p1_hand: String, p2_hand: String):
	# Create victory screen
	var victory_panel = Panel.new()
	victory_panel.size = Vector2(800, 600)
	victory_panel.position = Vector2(50, 50)
	victory_panel.modulate = Color(0.9, 0.9, 0.9, 0.95)
	add_child(victory_panel)

	var vbox = VBoxContainer.new()
	vbox.position = Vector2(20, 20)
	vbox.size = Vector2(760, 560)
	victory_panel.add_child(vbox)

	# Title
	var title = Label.new()
	title.text = "🎯 ROUND " + str(current_round) + " RESULTS 🎯"
	title.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	title.add_theme_font_size_override("font_size", 24)
	vbox.add_child(title)

	# Player 1 section
	var p1_section = VBoxContainer.new()
	vbox.add_child(p1_section)

	var p1_title = Label.new()
	p1_title.text = "👤 PLAYER 1 " + (("(CHEATED ⚠️)" if player1_modification_type == "illegal" else "(LEGAL ✅)"))
	p1_title.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	p1_section.add_child(p1_title)

	var p1_cards_container = HBoxContainer.new()
	p1_cards_container.alignment = BoxContainer.ALIGNMENT_CENTER
	p1_section.add_child(p1_cards_container)

	# Display player 1 cards
	for card in p1_cards:
		var card_label = Label.new()
		card_label.text = get_card_text(card)
		if card.get("modified", false):
			card_label.modulate = Color.RED
		p1_cards_container.add_child(card_label)

	var p1_hand_label = Label.new()
	p1_hand_label.text = "Hand: " + p1_hand
	p1_hand_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	p1_section.add_child(p1_hand_label)

	# Player 2 section
	var p2_section = VBoxContainer.new()
	vbox.add_child(p2_section)

	var p2_title = Label.new()
	p2_title.text = "🤖 PLAYER 2 (AI) " + (("(CHEATED ⚠️)" if player2_modification_type == "illegal" else "(LEGAL ✅)"))
	p2_title.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	p2_section.add_child(p2_title)

	var p2_cards_container = HBoxContainer.new()
	p2_cards_container.alignment = BoxContainer.ALIGNMENT_CENTER
	p2_section.add_child(p2_cards_container)

	# Display player 2 cards
	for card in p2_cards:
		var card_label = Label.new()
		card_label.text = get_card_text(card)
		if card.get("modified", false):
			card_label.modulate = Color.RED
		p2_cards_container.add_child(card_label)

	var p2_hand_label = Label.new()
	p2_hand_label.text = "Hand: " + p2_hand
	p2_hand_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	p2_section.add_child(p2_hand_label)

	# Winner announcement
	var winner_label = Label.new()
	winner_label.text = reason
	winner_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	vbox.add_child(winner_label)

	# Score
	var score_label = Label.new()
	score_label.text = "📊 SCORE: " + str(player1_score) + " - " + str(player2_score) + " 📊"
	score_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	vbox.add_child(score_label)

	# Continue button
	var continue_button = Button.new()
	continue_button.custom_minimum_size = Vector2(200, 50)
	if current_round >= 3 or player1_score >= 2 or player2_score >= 2:
		continue_button.text = "🔄 Play Again"
		continue_button.pressed.connect(restart_game)
	else:
		continue_button.text = "▶️ Next Round"
		continue_button.pressed.connect(next_round)

	var continue_container = HBoxContainer.new()
	continue_container.alignment = BoxContainer.ALIGNMENT_CENTER
	continue_container.add_child(continue_button)
	vbox.add_child(continue_container)

func next_round():
	# Remove victory screen
	for child in get_children():
		if child is Panel and child.name != "Background":
			child.queue_free()

	current_round += 1
	print("Starting round ", current_round)
	start_round()

func restart_game():
	get_tree().reload_current_scene()

# Debug function
func _input(event):
	if event.is_action_pressed("ui_accept"):  # Enter key
		print_debug_info()

func print_debug_info():
	print("=== DEBUG INFO ===")
	print("Phase: ", get_phase_name())
	print("Round: ", current_round, "/3")
	print("Score: ", player1_score, " - ", player2_score)
	print("Player 1 selected: ", player1_selected_cards.size(), " cards")
	print("Player 2 selected: ", player2_selected_cards.size(), " cards")
	print("Player 1 proceeded: ", player1_has_proceeded)
	print("Player 2 proceeded: ", player2_has_proceeded)
	print("Player 1 modified: ", player1_has_modified, " (", player1_modification_type, ")")
	print("Player 2 modified: ", player2_has_modified, " (", player2_modification_type, ")")
	print("Player 1 validated: ", player1_validated)
	print("Player 2 validated: ", player2_validated)
	print("UI selected cards: ", selected_card_ids.size())
	print("===================")

func show_message(text: String):
	print("Message: " + text)
	instruction_label.text = text
