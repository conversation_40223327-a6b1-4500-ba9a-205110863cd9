[gd_scene load_steps=4 format=3 uid="uid://bqp18juvcibk1"]

[ext_resource type="Script" uid="uid://140i7h120guh" path="res://Scripts/MainMenu.gd" id="1_gtj7m"]
[ext_resource type="Texture2D" uid="uid://boplpqtho2wo3" path="res://Assets/MenuBackground.jpg" id="2_nkt8v"]
[ext_resource type="Texture2D" uid="uid://7fk01r8232rv" path="res://Assets/BLUFF ROYALE Logo.png" id="3_r2mrw"]

[node name="Root" type="Control"]
layout_mode = 3
anchors_preset = 0
offset_left = 2.0
offset_top = -1.0
offset_right = 1026.0
offset_bottom = 769.0
script = ExtResource("1_gtj7m")

[node name="MenuBackground" type="Sprite2D" parent="."]
position = Vector2(996, 982)
texture = ExtResource("2_nkt8v")

[node name="BluffRoyale" type="Sprite2D" parent="."]
position = Vector2(640, 250)
scale = Vector2(0.438424, 0.438424)
texture = ExtResource("3_r2mrw")

[node name="MainLayout" type="VBoxContainer" parent="."]
layout_mode = 0
offset_left = 401.0
offset_top = 388.0
offset_right = 913.0
offset_bottom = 717.0
alignment = 1

[node name="StartButton" type="Button" parent="MainLayout"]
layout_mode = 2
text = "Commencer la partie"

[node name="QuitButton" type="Button" parent="MainLayout"]
layout_mode = 2
text = "Quitter le jeu"

[node name="OptionsButton" type="Button" parent="MainLayout"]
layout_mode = 2
text = "Options"

[node name="ModeSelection" type="Panel" parent="."]
visible = false
layout_mode = 0

[node name="SoloButton" type="Button" parent="ModeSelection"]
layout_mode = 0
offset_left = 140.0
offset_top = 614.0
offset_right = 181.0
offset_bottom = 645.0
text = "Solo"

[node name="MultiButton" type="Button" parent="ModeSelection"]
layout_mode = 0
offset_left = 228.0
offset_top = 614.0
offset_right = 277.0
offset_bottom = 645.0
text = "Multi"

[node name="MultiSelection" type="Panel" parent="."]
visible = false
layout_mode = 0

[node name="PublicButton" type="Button" parent="MultiSelection"]
layout_mode = 0
offset_left = 286.0
offset_top = 621.0
offset_right = 341.0
offset_bottom = 652.0
text = "Public"

[node name="PrivateButton" type="Button" parent="MultiSelection"]
layout_mode = 0
offset_left = 260.0
offset_top = 653.0
offset_right = 307.0
offset_bottom = 684.0
text = "Privé"

[node name="PrivateRoom" type="Panel" parent="."]
visible = false
layout_mode = 0

[node name="CreateRoomButton" type="Button" parent="PrivateRoom"]
layout_mode = 0
offset_left = 360.0
offset_top = 692.0
offset_right = 484.0
offset_bottom = 723.0
text = "Créer une salle"

[node name="JoinRoomButton" type="Button" parent="PrivateRoom"]
layout_mode = 0
offset_left = 347.0
offset_top = 658.0
offset_right = 503.0
offset_bottom = 689.0
text = "Rejoindre une salle"

[node name="RoomCode" type="LineEdit" parent="PrivateRoom"]
layout_mode = 0
offset_left = 512.0
offset_top = 660.0
offset_right = 757.0
offset_bottom = 691.0
placeholder_text = "Entrez le code de la salle"

[connection signal="pressed" from="MainLayout/StartButton" to="." method="_on_start_button_pressed"]
[connection signal="pressed" from="MainLayout/QuitButton" to="." method="_on_quit_button_pressed"]
[connection signal="pressed" from="MainLayout/OptionsButton" to="." method="_on_options_button_pressed"]
[connection signal="pressed" from="ModeSelection/SoloButton" to="." method="_on_solo_button_pressed"]
[connection signal="pressed" from="ModeSelection/MultiButton" to="." method="_on_multi_button_pressed"]
[connection signal="pressed" from="MultiSelection/PublicButton" to="." method="_on_public_button_pressed"]
[connection signal="pressed" from="MultiSelection/PrivateButton" to="." method="_on_private_button_pressed"]
[connection signal="pressed" from="PrivateRoom/CreateRoomButton" to="." method="_on_create_room_button_pressed"]
[connection signal="pressed" from="PrivateRoom/JoinRoomButton" to="." method="_on_join_room_button_pressed"]
