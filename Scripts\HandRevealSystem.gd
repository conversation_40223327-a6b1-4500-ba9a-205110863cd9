extends Control
class_name HandRevealSystem

signal reveal_completed(results: Dictionary)

# UI References
@onready var reveal_container: Control = $RevealContainer
@onready var player1_hand_display: Control = $RevealContainer/Player1HandDisplay
@onready var player2_hand_display: Control = $RevealContainer/Player2HandDisplay
@onready var vs_label: Label = $RevealContainer/VSLabel
@onready var result_label: Label = $RevealContainer/ResultLabel
@onready var cheat_indicator_p1: Label = $RevealContainer/CheatIndicatorP1
@onready var cheat_indicator_p2: Label = $RevealContainer/CheatIndicatorP2
@onready var continue_button: Button = $RevealContainer/ContinueButton

# Animation and timing
var reveal_duration: float = 2.0
var card_reveal_delay: float = 0.2
var result_delay: float = 1.0

# Current reveal data
var current_results: Dictionary = {}

func _ready():
	setup_ui()
	hide_all_elements()

func setup_ui():
	continue_button.pressed.connect(_on_continue_pressed)
	vs_label.text = "VS"
	result_label.text = ""
	cheat_indicator_p1.text = ""
	cheat_indicator_p2.text = ""

func hide_all_elements():
	reveal_container.visible = false
	player1_hand_display.modulate.a = 0.0
	player2_hand_display.modulate.a = 0.0
	vs_label.modulate.a = 0.0
	result_label.modulate.a = 0.0
	cheat_indicator_p1.modulate.a = 0.0
	cheat_indicator_p2.modulate.a = 0.0
	continue_button.visible = false

# Main reveal function
func reveal_hands(results: Dictionary):
	current_results = results
	reveal_container.visible = true
	
	# Start the reveal sequence
	start_reveal_sequence()

func start_reveal_sequence():
	var tween = create_tween()
	tween.set_parallel(true)
	
	# Phase 1: Show hand displays
	tween.tween_property(player1_hand_display, "modulate:a", 1.0, 0.5)
	tween.tween_property(player2_hand_display, "modulate:a", 1.0, 0.5)
	
	# Phase 2: Show VS label
	tween.tween_delay(0.3)
	tween.tween_property(vs_label, "modulate:a", 1.0, 0.3)
	
	# Phase 3: Reveal cards
	tween.tween_delay(0.5)
	tween.tween_callback(reveal_player_cards)
	
	# Phase 4: Show cheat indicators if applicable
	tween.tween_delay(1.0)
	tween.tween_callback(reveal_cheat_indicators)
	
	# Phase 5: Show results
	tween.tween_delay(1.5)
	tween.tween_callback(show_results)

func reveal_player_cards():
	# Display Player 1's hand
	display_hand_cards(player1_hand_display, current_results.get("player1_cards", []), "Player 1")
	
	# Display Player 2's hand
	display_hand_cards(player2_hand_display, current_results.get("player2_cards", []), "Player 2")

func display_hand_cards(container: Control, cards: Array, player_name: String):
	# Clear existing cards
	for child in container.get_children():
		child.queue_free()
	
	# Create header
	var header = Label.new()
	header.text = player_name
	header.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	header.add_theme_font_size_override("font_size", 18)
	container.add_child(header)
	
	# Create hand value label
	var hand_value_label = Label.new()
	var hand_value = current_results.get("player1_hand" if player_name == "Player 1" else "player2_hand", "")
	hand_value_label.text = hand_value
	hand_value_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	hand_value_label.add_theme_font_size_override("font_size", 16)
	hand_value_label.add_theme_color_override("font_color", Color.GOLD)
	container.add_child(hand_value_label)
	
	# Create cards container
	var cards_container = HBoxContainer.new()
	cards_container.alignment = BoxContainer.ALIGNMENT_CENTER
	container.add_child(cards_container)
	
	# Add cards with animation
	for i in range(cards.size()):
		var card = cards[i]
		var card_display = create_card_display(card)
		cards_container.add_child(card_display)
		
		# Animate card appearance
		card_display.modulate.a = 0.0
		card_display.scale = Vector2(0.5, 0.5)
		
		var tween = create_tween()
		tween.set_parallel(true)
		tween.tween_delay(i * card_reveal_delay)
		tween.tween_property(card_display, "modulate:a", 1.0, 0.3)
		tween.tween_property(card_display, "scale", Vector2(1.0, 1.0), 0.3)

func create_card_display(card: Dictionary) -> Control:
	var card_container = VBoxContainer.new()
	card_container.custom_minimum_size = Vector2(60, 80)
	
	# Card background
	var card_bg = Panel.new()
	card_bg.custom_minimum_size = Vector2(50, 70)
	card_container.add_child(card_bg)
	
	# Card text
	var card_label = Label.new()
	card_label.text = get_card_display_text(card)
	card_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	card_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	card_label.add_theme_font_size_override("font_size", 12)
	
	# Color based on suit
	var suit = card.get("suit", "hearts")
	if suit in ["hearts", "diamonds"]:
		card_label.add_theme_color_override("font_color", Color.RED)
	else:
		card_label.add_theme_color_override("font_color", Color.BLACK)
	
	card_bg.add_child(card_label)
	
	# Modified indicator
	if card.get("modified", false):
		var modified_indicator = Label.new()
		modified_indicator.text = "⚠"
		modified_indicator.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
		modified_indicator.add_theme_color_override("font_color", Color.ORANGE)
		card_container.add_child(modified_indicator)
	
	return card_container

func get_card_display_text(card: Dictionary) -> String:
	var rank = card.get("value", 1)
	var suit = card.get("suit", "hearts")
	
	var rank_text = ""
	match rank:
		1, 14:
			rank_text = "A"
		11:
			rank_text = "J"
		12:
			rank_text = "Q"
		13:
			rank_text = "K"
		_:
			rank_text = str(rank)
	
	var suit_symbol = ""
	match suit:
		"hearts":
			suit_symbol = "♥"
		"diamonds":
			suit_symbol = "♦"
		"clubs":
			suit_symbol = "♣"
		"spades":
			suit_symbol = "♠"
	
	return rank_text + "\n" + suit_symbol

func reveal_cheat_indicators():
	var p1_cheated = current_results.get("player1_cheated", false)
	var p2_cheated = current_results.get("player2_cheated", false)
	
	if p1_cheated:
		var cheat_type = current_results.get("player1_cheat_type", "")
		cheat_indicator_p1.text = "CHEATED (" + cheat_type + ")"
		cheat_indicator_p1.add_theme_color_override("font_color", Color.RED)
		
		var tween = create_tween()
		tween.tween_property(cheat_indicator_p1, "modulate:a", 1.0, 0.5)
	
	if p2_cheated:
		var cheat_type = current_results.get("player2_cheat_type", "")
		cheat_indicator_p2.text = "CHEATED (" + cheat_type + ")"
		cheat_indicator_p2.add_theme_color_override("font_color", Color.RED)
		
		var tween = create_tween()
		tween.tween_property(cheat_indicator_p2, "modulate:a", 1.0, 0.5)

func show_results():
	var winner = current_results.get("winner", 0)
	var reason = current_results.get("reason", "")
	
	# Set result text and color
	result_label.text = reason
	
	match winner:
		1:
			result_label.add_theme_color_override("font_color", Color.GREEN)
		2:
			result_label.add_theme_color_override("font_color", Color.BLUE)
		_:
			result_label.add_theme_color_override("font_color", Color.YELLOW)
	
	# Animate result appearance
	var tween = create_tween()
	tween.set_parallel(true)
	tween.tween_property(result_label, "modulate:a", 1.0, 0.5)
	
	# Show continue button after a delay
	tween.tween_delay(1.0)
	tween.tween_callback(show_continue_button)

func show_continue_button():
	continue_button.visible = true
	continue_button.modulate.a = 0.0
	
	var tween = create_tween()
	tween.tween_property(continue_button, "modulate:a", 1.0, 0.3)

func _on_continue_pressed():
	emit_signal("reveal_completed", current_results)
	hide_reveal()

func hide_reveal():
	var tween = create_tween()
	tween.tween_property(reveal_container, "modulate:a", 0.0, 0.5)
	tween.tween_callback(func(): reveal_container.visible = false)

# Public interface
func is_revealing() -> bool:
	return reveal_container.visible

func skip_to_results():
	# Skip animations and show final results immediately
	player1_hand_display.modulate.a = 1.0
	player2_hand_display.modulate.a = 1.0
	vs_label.modulate.a = 1.0
	
	reveal_player_cards()
	reveal_cheat_indicators()
	show_results()
